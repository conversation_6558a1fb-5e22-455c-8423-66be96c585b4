#!/usr/bin/env python3
"""
Test the fixed theme system with proper TreeView styling
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_theme_fixes():
    """Test the theme system fixes"""
    print("🔧 Testing Theme System Fixes")
    print("=" * 50)
    
    # Test theme manager
    from gexplorer.themes import ThemeManager
    theme_manager = ThemeManager()
    
    print("📋 Available Themes:")
    themes = theme_manager.get_available_themes()
    for theme_id, theme_name in themes.items():
        theme_data = theme_manager.get_theme(theme_id)
        base_theme = theme_data.get('base_theme', 'unknown')
        print(f"  - {theme_id}: {theme_name} (base: {base_theme})")
    
    print(f"\n🎨 Testing Dark Theme Colors:")
    dark_theme = theme_manager.get_theme('dark')
    colors = dark_theme.get('colors', {})
    print(f"  - Background: {colors.get('bg_primary', 'N/A')}")
    print(f"  - Text: {colors.get('fg_primary', 'N/A')}")
    print(f"  - Selected: {colors.get('selected_bg', 'N/A')}")
    
    print(f"\n🚀 Starting GExplorer with improved theme system...")
    print("\n🔧 FIXES IMPLEMENTED:")
    print("✅ TreeView background styling (no more white backgrounds!)")
    print("✅ Improved dark theme colors for better contrast")
    print("✅ Entry and Combobox field background styling")
    print("✅ Proper theme persistence and loading")
    print("✅ Base theme change detection with restart option")
    
    print(f"\n🧪 TESTING INSTRUCTIONS:")
    print("1. Notice the app starts with proper theme colors")
    print("2. Click '🎨 Themes' button")
    print("3. Try switching to 'Dark Theme':")
    print("   - Should ask to restart for base theme change")
    print("   - Click 'Yes' to restart with dark base theme")
    print("4. After restart, file list should have dark background")
    print("5. Try other themes - they should apply immediately")
    print("6. Check that text is always readable (no white on white)")
    
    # Import and start the application
    from ttkbootstrap import Style
    from gexplorer.main import GExplorerApp, get_saved_theme
    
    # Start with the saved theme's base theme
    base_theme = get_saved_theme()
    print(f"\n🎯 Starting with base theme: {base_theme}")
    
    style = Style(theme=base_theme)
    root = style.master
    app = GExplorerApp(root, style)
    
    print("Close the window when done testing.")
    app.run()

if __name__ == "__main__":
    test_theme_fixes()
