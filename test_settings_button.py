#!/usr/bin/env python3
"""
Quick test to verify the Settings button functionality
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_settings_button():
    """Test that the Settings button opens the settings dialog"""
    print("🧪 TESTING SETTINGS BUTTON FUNCTIONALITY")
    print("=" * 50)
    
    print("✅ Starting GExplorer with Settings integration...")
    
    # Import and start the application
    from ttkbootstrap import Style
    from gexplorer.main import GExplorerApp, get_saved_theme
    
    # Start with the saved theme's base theme
    base_theme = get_saved_theme()
    print(f"📋 Base theme: {base_theme}")
    
    style = Style(theme=base_theme)
    root = style.master
    app = GExplorerApp(root, style)
    
    print("\n🎯 TESTING INSTRUCTIONS:")
    print("1. Look for the clean toolbar without theme/view controls")
    print("2. Find the '⚙️ Settings' button in the toolbar")
    print("3. Click the Settings button to open the dialog")
    print("4. Test each tab:")
    print("   📋 View: Change view mode, toggle preview")
    print("   🎨 Themes: Select themes, test 'Edit Themes' button")
    print("   ⚙️ General: Toggle options")
    print("   🔧 Advanced: Change refresh interval")
    print("5. Click 'Apply' to test changes")
    print("6. Click 'OK' to save and close")
    print("7. Close and restart app to test persistence")
    
    print("\n✅ Expected Results:")
    print("- Clean toolbar with only essential buttons")
    print("- Settings dialog opens with 4 organized tabs")
    print("- All settings work and persist")
    print("- Theme changes apply immediately")
    print("- View mode changes work")
    
    print("\n🚀 App started! Test the Settings button now.")
    app.run()

if __name__ == "__main__":
    test_settings_button()
