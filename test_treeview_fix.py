#!/usr/bin/env python3
"""
Test the TreeView color fix specifically
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_treeview_colors():
    """Test TreeView color application"""
    print("🌳 Testing TreeView Color Fix")
    print("=" * 50)
    
    # Test theme manager
    from gexplorer.themes import ThemeManager
    theme_manager = ThemeManager()
    
    print("🎨 Testing Dark Theme Colors:")
    dark_theme = theme_manager.get_theme('dark')
    colors = dark_theme.get('colors', {})
    
    bg_color = colors.get('bg_primary', '#ffffff')
    fg_color = colors.get('fg_primary', '#212529')
    
    print(f"  - Background: {bg_color}")
    print(f"  - Foreground: {fg_color}")
    print(f"  - Should be: Dark background with white text")
    
    print(f"\n🚀 Starting GExplorer with TreeView fix...")
    print("\n🔧 TREEVIEW FIXES IMPLEMENTED:")
    print("✅ Enhanced TreeView.configure() with background/foreground")
    print("✅ Added TreeView.Item styling")
    print("✅ Comprehensive style.map() for all states")
    print("✅ Direct widget.configure() calls after creation")
    print("✅ Tag-based color configuration")
    
    print(f"\n🧪 TESTING INSTRUCTIONS:")
    print("1. App should start with light theme (white background, dark text)")
    print("2. Click '🎨 Themes' button")
    print("3. Select 'Dark Theme' and click 'Apply Theme'")
    print("4. Click 'Yes' to restart with dark base theme")
    print("5. After restart:")
    print("   ✅ File list backgrounds should be DARK (#2b2b2b)")
    print("   ✅ File list text should be WHITE (#ffffff)")
    print("   ✅ NO MORE white text on white background!")
    print("6. Try selecting files - selection should be visible")
    print("7. Try other themes to verify they work correctly")
    
    # Import and start the application
    from ttkbootstrap import Style
    from gexplorer.main import GExplorerApp, get_saved_theme
    
    # Start with the saved theme's base theme
    base_theme = get_saved_theme()
    print(f"\n🎯 Starting with base theme: {base_theme}")
    
    style = Style(theme=base_theme)
    root = style.master
    app = GExplorerApp(root, style)
    
    print("\n🎨 Current theme applied. Check the file lists!")
    print("Close the window when done testing.")
    app.run()

if __name__ == "__main__":
    test_treeview_colors()
