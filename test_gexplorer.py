#!/usr/bin/env python3
"""
Simple test script for GExplorer functionality
"""

import os
import sys
import tempfile
import shutil

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_functionality():
    """Test basic functionality without GUI"""
    print("Testing GExplorer basic functionality...")
    
    # Test imports
    try:
        from gexplorer.logic.app_logic import AppLogic
        from gexplorer.filesystem import handler as fs_handler
        print("✓ Imports successful")
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

    # Test filesystem handler
    try:
        current_dir = os.getcwd()
        files = fs_handler.list_directory(current_dir)
        print(f"✓ Filesystem handler works - found {len(files)} items")
    except Exception as e:
        print(f"✗ Filesystem handler failed: {e}")
        return False
    
    # Test app logic
    try:
        def dummy_callback():
            pass
        
        app_logic = AppLogic(dummy_callback)
        print("✓ App logic initialization successful")
    except Exception as e:
        print(f"✗ App logic failed: {e}")
        return False
    
    # Test file operations with temporary files
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test file
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("Test content")
            
            # Test copy
            copy_dest = os.path.join(temp_dir, "test_copy.txt")
            app_logic.copy_file(test_file, copy_dest)
            
            if os.path.exists(copy_dest):
                print("✓ File copy works")
            else:
                print("✗ File copy failed")
                return False
            
            # Test rename
            rename_dest = os.path.join(temp_dir, "test_renamed.txt")
            app_logic.rename_file(copy_dest, "test_renamed.txt")
            
            if os.path.exists(rename_dest):
                print("✓ File rename works")
            else:
                print("✗ File rename failed")
                return False
            
            # Test delete
            app_logic.delete_file(rename_dest)
            
            if not os.path.exists(rename_dest):
                print("✓ File delete works")
            else:
                print("✗ File delete failed")
                return False
                
    except Exception as e:
        print(f"✗ File operations failed: {e}")
        return False
    
    print("✓ All basic functionality tests passed!")
    return True

def test_gui_startup():
    """Test GUI startup without showing window"""
    print("\nTesting GUI startup...")
    
    try:
        import tkinter as tk
        from gexplorer.main import GExplorerApp
        
        # Create root window but don't show it
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Create app instance
        app = GExplorerApp(root)
        
        # Test that key components exist
        assert hasattr(app, 'left_file_view'), "Left file view not created"
        assert hasattr(app, 'right_file_view'), "Right file view not created"
        assert hasattr(app, 'left_path'), "Left path not initialized"
        assert hasattr(app, 'right_path'), "Right path not initialized"
        
        print("✓ GUI components created successfully")
        
        # Clean up
        root.destroy()
        
    except Exception as e:
        print(f"✗ GUI startup failed: {e}")
        return False
    
    print("✓ GUI startup test passed!")
    return True

if __name__ == "__main__":
    print("GExplorer Test Suite")
    print("=" * 50)
    
    success = True
    
    # Run tests
    success &= test_basic_functionality()
    success &= test_gui_startup()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! GExplorer is working correctly.")
    else:
        print("❌ Some tests failed. Please check the issues above.")
    
    sys.exit(0 if success else 1)
