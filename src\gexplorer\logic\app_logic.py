import os
import shutil
import subprocess
from tkinter import messagebox
from gexplorer.filesystem.handler import list_directory, copy_item, move_item, delete_item, rename_item

class AppLogic:
    """
    Handles the application logic to coordinate between UI components and file system operations.
    """
    def __init__(self, ui_callback):
        """
        Initialize the application logic with a callback to update UI components.
        
        Args:
            ui_callback: A function to call to refresh UI components after file system changes.
        """
        self.ui_callback = ui_callback
        
    def open_file(self, file_path):
        """
        Open a file using the default system application.
        
        Args:
            file_path (str): Path to the file to open.
        """
        try:
            os.startfile(file_path)  # Windows-specific; use 'open' on macOS or 'xdg-open' on Linux
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open file: {str(e)}")
            
    def copy_file(self, source_path, dest_path):
        """
        Copy a file or directory to a new location.
        
        Args:
            source_path (str): Path to the source file or directory.
            dest_path (str): Path to the destination location.
        """
        try:
            copy_item(source_path, dest_path)
            self.ui_callback()
            messagebox.showinfo("Success", "File copied successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy file: {str(e)}")
            
    def move_file(self, source_path, dest_path):
        """
        Move a file or directory to a new location.
        
        Args:
            source_path (str): Path to the source file or directory.
            dest_path (str): Path to the destination location.
        """
        try:
            move_item(source_path, dest_path)
            self.ui_callback()
            messagebox.showinfo("Success", "File moved successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to move file: {str(e)}")
            
    def delete_file(self, file_path):
        """
        Delete a file or directory.
        
        Args:
            file_path (str): Path to the file or directory to delete.
        """
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete {file_path}?"):
            try:
                delete_item(file_path)
                self.ui_callback()
                messagebox.showinfo("Success", "File deleted successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete file: {str(e)}")
                
    def rename_file(self, old_path, new_name):
        """
        Rename a file or directory.
        
        Args:
            old_path (str): Current path to the file or directory.
            new_name (str): New name for the file or directory.
        """
        try:
            new_path = os.path.join(os.path.dirname(old_path), new_name)
            rename_item(old_path, new_path)
            self.ui_callback()
            messagebox.showinfo("Success", "File renamed successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to rename file: {str(e)}")
            
    def get_directory_contents(self, path):
        """
        Get the contents of a directory.
        
        Args:
            path (str): Path to the directory.
            
        Returns:
            list: List of dictionaries containing information about directory contents.
        """
        return list_directory(path)
