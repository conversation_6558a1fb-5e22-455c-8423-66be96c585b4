<!--
Instructions: Use this template to guide the population of descriptive HDTA content (Purpose, Interfaces, Implementation Details, Status, Plan Links) for {module_name}_module.md. The file itself, including the mini-tracker, may be initially generated by the dependency_processor. When detailing 'Implementation Plans & Tasks', focus on linking plans for significant features or components within this module. This document describes a major functional area of the system and includes a mini-tracker at the end (managed by dependency_processor) to track internal file dependencies.
-->

# Module: {ModuleName}

## Purpose & Responsibility
{1-2 paragraphs on module purpose & responsibility}

## Interfaces
* `{InterfaceName}`: {purpose}
  * `{Method1}`: {description}
  * `{Method2}`: {description}
* Input: [Data received]
* Output: [Data provided]
...

## Implementation Details
* Files: [List with 1-line descriptions]
* Important algorithms: [List with 1-line descriptions]
* Data Models
    * `{Model1}`: {description}
    * `{Model2}`: {description}

## Current Implementation Status
* Completed: [List of completed items]
* In Progress: [Current work]
* Pending: [Future work]

## Implementation Plans & Tasks
* `implementation_plan_{filename1}.md`
  * [Task1]: {brief description}
  * [Task2]: {brief description}
* `implementation_plan_{filename2}.md`
  * [Task1]: {brief description}
  * [Task2]: {brief description} 
...

## Mini Dependency Tracker
---mini_tracker_start---


---mini_tracker_end---