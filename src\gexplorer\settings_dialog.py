"""
Comprehensive Settings dialog for GExplorer
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable, Dict, Any
from gexplorer.themes import ThemeManager
from gexplorer.theme_dialog import ThemeDialog
from gexplorer.settings_manager import SettingsManager


class SettingsDialog:
    """Comprehensive settings dialog with multiple tabs"""
    
    def __init__(self, parent, app_instance):
        self.parent = parent
        self.app = app_instance
        self.theme_manager = ThemeManager()
        self.settings_manager = SettingsManager()

        self.dialog = None
        self.settings_changed = False

        # Store original settings for cancel functionality
        self.original_settings = self.get_current_settings()

        self.create_dialog()
    
    def get_current_settings(self) -> Dict[str, Any]:
        """Get current application settings"""
        return {
            'theme_id': self.app.current_theme_id,
            'view_mode': self.app.view_mode.get(),
            'show_preview': self.app.show_preview.get(),
            'window_geometry': self.app.root.geometry(),
            'left_path': self.app.left_path,
            'right_path': self.app.right_path,
            'active_pane': self.app.active_pane
        }
    
    def create_dialog(self):
        """Create the settings dialog window"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("GExplorer Settings")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        # Create main container
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for different settings categories
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create tabs
        self.create_view_tab()
        self.create_theme_tab()
        self.create_general_tab()
        self.create_advanced_tab()
        
        # Dialog buttons
        self.create_dialog_buttons(main_frame)
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
    
    def create_view_tab(self):
        """Create the View Settings tab"""
        self.view_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.view_frame, text="View")
        
        # View Mode Section
        view_section = ttk.LabelFrame(self.view_frame, text="View Mode", padding=10)
        view_section.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(view_section, text="Default view mode for file lists:").pack(anchor=tk.W)
        
        self.view_mode_var = tk.StringVar(value=self.app.view_mode.get())
        view_modes = ["Detailed List", "Simple List", "Large Icons"]
        
        for mode in view_modes:
            ttk.Radiobutton(view_section, text=mode, variable=self.view_mode_var, 
                           value=mode, command=self.on_setting_change).pack(anchor=tk.W, pady=2)
        
        # Preview Section
        preview_section = ttk.LabelFrame(self.view_frame, text="Preview Options", padding=10)
        preview_section.pack(fill=tk.X, pady=(0, 10))
        
        self.show_preview_var = tk.BooleanVar(value=self.app.show_preview.get())
        ttk.Checkbutton(preview_section, text="Show file preview pane", 
                       variable=self.show_preview_var, 
                       command=self.on_setting_change).pack(anchor=tk.W)
        
        # Layout Section
        layout_section = ttk.LabelFrame(self.view_frame, text="Layout Options", padding=10)
        layout_section.pack(fill=tk.X, pady=(0, 10))

        # Layout Mode Selection
        ttk.Label(layout_section, text="Layout Mode:", font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)

        self.layout_mode_var = tk.StringVar(value=self.settings_manager.get_layout_mode())
        layout_modes = [
            ("Total Commander (Dual Pane)", "total_commander"),
            ("Windows Explorer (Single Pane + Tree)", "windows_explorer")
        ]

        for text, value in layout_modes:
            ttk.Radiobutton(layout_section, text=text, variable=self.layout_mode_var,
                           value=value, command=self.on_setting_change).pack(anchor=tk.W, pady=2)

        # Pane Orientation (for Total Commander mode)
        ttk.Label(layout_section, text="Pane Orientation:", font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W, pady=(10, 0))

        self.pane_orientation_var = tk.StringVar(value=self.settings_manager.get_pane_orientation())
        orientations = [("Horizontal (Side by Side)", "horizontal"), ("Vertical (Top/Bottom)", "vertical")]

        for text, value in orientations:
            ttk.Radiobutton(layout_section, text=text, variable=self.pane_orientation_var,
                           value=value, command=self.on_setting_change).pack(anchor=tk.W, pady=2)

        # Toolbar Options
        toolbar_frame = ttk.Frame(layout_section)
        toolbar_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(toolbar_frame, text="Toolbar Position:", font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)

        self.toolbar_position_var = tk.StringVar(value=self.settings_manager.get_toolbar_position())
        toolbar_combo = ttk.Combobox(toolbar_frame, textvariable=self.toolbar_position_var,
                                   values=["top", "bottom", "left", "right", "hidden"],
                                   state="readonly", width=15)
        toolbar_combo.pack(anchor=tk.W, pady=(2, 5))
        toolbar_combo.bind('<<ComboboxSelected>>', lambda e: self.on_setting_change())

        # Toolbar appearance options
        self.show_toolbar_text_var = tk.BooleanVar(value=self.settings_manager.should_show_toolbar_text())
        ttk.Checkbutton(toolbar_frame, text="Show text labels on toolbar buttons",
                       variable=self.show_toolbar_text_var,
                       command=self.on_setting_change).pack(anchor=tk.W)

        ttk.Label(toolbar_frame, text="Toolbar Icon Size:").pack(anchor=tk.W, pady=(5, 0))
        self.toolbar_icon_size_var = tk.StringVar(value=self.settings_manager.get_toolbar_icon_size())
        icon_size_combo = ttk.Combobox(toolbar_frame, textvariable=self.toolbar_icon_size_var,
                                     values=["small", "medium", "large"],
                                     state="readonly", width=15)
        icon_size_combo.pack(anchor=tk.W, pady=(2, 0))
        icon_size_combo.bind('<<ComboboxSelected>>', lambda e: self.on_setting_change())

        # Additional Layout Options
        additional_section = ttk.LabelFrame(self.view_frame, text="Additional Options", padding=10)
        additional_section.pack(fill=tk.X, pady=(10, 0))

        # Status bar visibility
        self.status_bar_visible_var = tk.BooleanVar(value=self.settings_manager.is_status_bar_visible())
        ttk.Checkbutton(additional_section, text="Show status bar",
                       variable=self.status_bar_visible_var,
                       command=self.on_setting_change).pack(anchor=tk.W)

        # Tree view for Windows Explorer mode
        self.show_tree_view_var = tk.BooleanVar(value=self.settings_manager.should_show_tree_view())
        ttk.Checkbutton(additional_section, text="Show folder tree (Windows Explorer mode)",
                       variable=self.show_tree_view_var,
                       command=self.on_setting_change).pack(anchor=tk.W)

        # Breadcrumb style
        breadcrumb_frame = ttk.Frame(additional_section)
        breadcrumb_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(breadcrumb_frame, text="Breadcrumb Style:").pack(anchor=tk.W)
        self.breadcrumb_style_var = tk.StringVar(value=self.settings_manager.get_breadcrumb_style())
        breadcrumb_combo = ttk.Combobox(breadcrumb_frame, textvariable=self.breadcrumb_style_var,
                                      values=["buttons", "text"],
                                      state="readonly", width=15)
        breadcrumb_combo.pack(anchor=tk.W, pady=(2, 0))
        breadcrumb_combo.bind('<<ComboboxSelected>>', lambda e: self.on_setting_change())

        # Preview position
        preview_frame = ttk.Frame(additional_section)
        preview_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(preview_frame, text="Preview Pane Position:").pack(anchor=tk.W)
        self.preview_position_var = tk.StringVar(value=self.settings_manager.get_preview_position())
        preview_combo = ttk.Combobox(preview_frame, textvariable=self.preview_position_var,
                                   values=["right", "bottom", "floating"],
                                   state="readonly", width=15)
        preview_combo.pack(anchor=tk.W, pady=(2, 0))
        preview_combo.bind('<<ComboboxSelected>>', lambda e: self.on_setting_change())

        # Pane split ratio
        split_frame = ttk.Frame(additional_section)
        split_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(split_frame, text="Pane Split Ratio:").pack(anchor=tk.W)
        self.pane_split_var = tk.DoubleVar(value=self.settings_manager.get_pane_split_ratio())
        split_scale = ttk.Scale(split_frame, from_=0.2, to=0.8, variable=self.pane_split_var,
                              orient=tk.HORIZONTAL, length=200,
                              command=lambda v: self.on_setting_change())
        split_scale.pack(anchor=tk.W, pady=(2, 0))

        split_label = ttk.Label(split_frame, text="50% / 50%")
        split_label.pack(anchor=tk.W)

        def update_split_label(*args):
            ratio = self.pane_split_var.get()
            left_pct = int(ratio * 100)
            right_pct = 100 - left_pct
            split_label.config(text=f"{left_pct}% / {right_pct}%")

        self.pane_split_var.trace('w', update_split_label)
        update_split_label()
    
    def create_theme_tab(self):
        """Create the Theme Settings tab"""
        self.theme_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.theme_frame, text="Themes")
        
        # Theme Selection Section
        theme_section = ttk.LabelFrame(self.theme_frame, text="Theme Selection", padding=10)
        theme_section.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Theme list
        list_frame = ttk.Frame(theme_section)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(list_frame, text="Available Themes:").pack(anchor=tk.W, pady=(0, 5))
        
        # Create listbox with scrollbar
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        self.theme_listbox = tk.Listbox(listbox_frame, height=8)
        theme_scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.theme_listbox.yview)
        self.theme_listbox.configure(yscrollcommand=theme_scrollbar.set)
        
        self.theme_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        theme_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.theme_listbox.bind('<<ListboxSelect>>', self.on_theme_select)
        
        # Theme buttons
        theme_buttons = ttk.Frame(theme_section)
        theme_buttons.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(theme_buttons, text="Apply Theme", 
                  command=self.apply_selected_theme).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(theme_buttons, text="Edit Themes...", 
                  command=self.open_theme_editor).pack(side=tk.LEFT, padx=(0, 5))
        
        # Load themes
        self.load_themes()
    
    def create_general_tab(self):
        """Create the General Settings tab"""
        self.general_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.general_frame, text="General")
        
        # Startup Section
        startup_section = ttk.LabelFrame(self.general_frame, text="Startup Options", padding=10)
        startup_section.pack(fill=tk.X, pady=(0, 10))
        
        self.remember_paths_var = tk.BooleanVar(value=self.settings_manager.get_setting('general.remember_paths', True))
        ttk.Checkbutton(startup_section, text="Remember last opened directories",
                       variable=self.remember_paths_var,
                       command=self.on_setting_change).pack(anchor=tk.W)

        self.restore_window_var = tk.BooleanVar(value=self.settings_manager.get_setting('general.restore_window', True))
        ttk.Checkbutton(startup_section, text="Restore window size and position",
                       variable=self.restore_window_var,
                       command=self.on_setting_change).pack(anchor=tk.W)

        # File Operations Section
        operations_section = ttk.LabelFrame(self.general_frame, text="File Operations", padding=10)
        operations_section.pack(fill=tk.X, pady=(0, 10))

        self.confirm_delete_var = tk.BooleanVar(value=self.settings_manager.get_setting('general.confirm_delete', True))
        ttk.Checkbutton(operations_section, text="Confirm file deletions",
                       variable=self.confirm_delete_var,
                       command=self.on_setting_change).pack(anchor=tk.W)

        self.show_hidden_var = tk.BooleanVar(value=self.settings_manager.get_setting('view.show_hidden_files', False))
        ttk.Checkbutton(operations_section, text="Show hidden files and folders",
                       variable=self.show_hidden_var,
                       command=self.on_setting_change).pack(anchor=tk.W)
    
    def create_advanced_tab(self):
        """Create the Advanced Settings tab"""
        self.advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.advanced_frame, text="Advanced")
        
        # Performance Section
        performance_section = ttk.LabelFrame(self.advanced_frame, text="Performance", padding=10)
        performance_section.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(performance_section, text="File list refresh interval (ms):").pack(anchor=tk.W)
        self.refresh_interval_var = tk.StringVar(value=str(self.settings_manager.get_setting('advanced.refresh_interval', 1000)))
        refresh_entry = ttk.Entry(performance_section, textvariable=self.refresh_interval_var, width=10)
        refresh_entry.pack(anchor=tk.W, pady=(2, 5))
        refresh_entry.bind('<KeyRelease>', lambda e: self.on_setting_change())

        # Debug Section
        debug_section = ttk.LabelFrame(self.advanced_frame, text="Debug Options", padding=10)
        debug_section.pack(fill=tk.X, pady=(0, 10))

        self.debug_mode_var = tk.BooleanVar(value=self.settings_manager.get_setting('advanced.debug_mode', False))
        ttk.Checkbutton(debug_section, text="Enable debug mode", 
                       variable=self.debug_mode_var,
                       command=self.on_setting_change).pack(anchor=tk.W)
        
        # Reset Section
        reset_section = ttk.LabelFrame(self.advanced_frame, text="Reset Options", padding=10)
        reset_section.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(reset_section, text="Reset All Settings to Default", 
                  command=self.reset_to_defaults).pack(anchor=tk.W)
    
    def create_dialog_buttons(self, parent):
        """Create OK, Cancel, Apply buttons"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="OK", command=self.on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self.on_cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Apply", command=self.on_apply).pack(side=tk.RIGHT, padx=(5, 0))
    
    def load_themes(self):
        """Load available themes into the listbox"""
        self.theme_listbox.delete(0, tk.END)
        self.themes = self.theme_manager.get_available_themes()
        
        for theme_id, theme_name in self.themes.items():
            display_text = f"{theme_name}"
            if theme_id == self.app.current_theme_id:
                display_text += " (Current)"
            self.theme_listbox.insert(tk.END, display_text)
            
            # Select current theme
            if theme_id == self.app.current_theme_id:
                self.theme_listbox.selection_set(self.theme_listbox.size() - 1)
    
    def on_theme_select(self, event):
        """Handle theme selection"""
        selection = self.theme_listbox.curselection()
        if selection:
            self.on_setting_change()
    
    def apply_selected_theme(self):
        """Apply the selected theme"""
        selection = self.theme_listbox.curselection()
        if selection:
            index = selection[0]
            theme_ids = list(self.themes.keys())
            if index < len(theme_ids):
                theme_id = theme_ids[index]
                self.app.apply_theme(theme_id)
                self.load_themes()  # Refresh the list to show new current theme
                self.on_setting_change()
    
    def open_theme_editor(self):
        """Open the theme editor dialog"""
        ThemeDialog(self.dialog, self.app.current_theme_id, self.app.apply_theme)
        self.load_themes()  # Refresh themes after editing
    
    def on_setting_change(self):
        """Mark that settings have been changed"""
        self.settings_changed = True
    
    def on_ok(self):
        """Apply settings and close dialog"""
        if self.apply_settings():
            self.dialog.destroy()
    
    def on_cancel(self):
        """Cancel changes and close dialog"""
        if self.settings_changed:
            result = messagebox.askyesnocancel(
                "Unsaved Changes",
                "You have unsaved changes. Do you want to apply them before closing?",
                parent=self.dialog
            )
            if result is True:  # Yes - apply and close
                if self.apply_settings():
                    self.dialog.destroy()
            elif result is False:  # No - discard and close
                self.restore_original_settings()
                self.dialog.destroy()
            # None - cancel, keep dialog open
        else:
            self.dialog.destroy()
    
    def on_apply(self):
        """Apply settings without closing dialog"""
        self.apply_settings()
    
    def apply_settings(self) -> bool:
        """Apply all settings changes"""
        try:
            # Check if layout mode changed
            new_layout_mode = self.layout_mode_var.get()
            if new_layout_mode != self.app.layout_mode:
                # Save settings first, then switch layout
                self.save_settings_to_config()
                self.app.switch_layout_mode(new_layout_mode)
            else:
                # Apply view mode
                if self.view_mode_var.get() != self.app.view_mode.get():
                    self.app.view_mode.set(self.view_mode_var.get())
                    self.app.on_view_mode_change()

                # Apply preview setting
                if self.show_preview_var.get() != self.app.show_preview.get():
                    self.app.show_preview.set(self.show_preview_var.get())
                    self.app.toggle_preview()

                # Apply layout settings
                if hasattr(self.app, 'apply_layout_settings'):
                    self.app.apply_layout_settings()

                # Save settings to config
                self.save_settings_to_config()

            self.settings_changed = False
            return True

        except Exception as e:
            # Try to show error dialog, but handle case where dialog is destroyed
            try:
                messagebox.showerror("Error", f"Failed to apply settings: {e}", parent=self.dialog)
            except:
                # If dialog is destroyed, show error without parent
                messagebox.showerror("Error", f"Failed to apply settings: {e}")
            return False
    
    def restore_original_settings(self):
        """Restore original settings"""
        try:
            # Restore theme
            if self.original_settings['theme_id'] != self.app.current_theme_id:
                self.app.apply_theme(self.original_settings['theme_id'])

            # Restore view mode only if we have valid file views
            if (hasattr(self.app, 'view_mode') and self.app.view_mode and
                hasattr(self.app, 'left_file_view') and self.app.left_file_view):
                if self.original_settings['view_mode'] != self.app.view_mode.get():
                    self.app.view_mode.set(self.original_settings['view_mode'])
                    self.app.on_view_mode_change()

            # Restore preview
            if (hasattr(self.app, 'show_preview') and self.app.show_preview and
                self.original_settings['show_preview'] != self.app.show_preview.get()):
                self.app.show_preview.set(self.original_settings['show_preview'])
                self.app.toggle_preview()

        except Exception as e:
            print(f"Error restoring settings: {e}")
    
    def save_settings_to_config(self):
        """Save settings to configuration file"""
        # Save view settings
        self.settings_manager.set_view_settings(
            default_view_mode=self.view_mode_var.get(),
            show_preview=self.show_preview_var.get(),
            show_hidden_files=self.show_hidden_var.get()
        )

        # Save layout settings
        self.settings_manager.set_layout_settings(
            mode=self.layout_mode_var.get(),
            pane_orientation=self.pane_orientation_var.get(),
            toolbar_position=self.toolbar_position_var.get(),
            status_bar_visible=self.status_bar_visible_var.get(),
            breadcrumb_style=self.breadcrumb_style_var.get(),
            show_tree_view=self.show_tree_view_var.get(),
            pane_split_ratio=self.pane_split_var.get(),
            preview_position=self.preview_position_var.get(),
            show_toolbar_text=self.show_toolbar_text_var.get(),
            toolbar_icon_size=self.toolbar_icon_size_var.get()
        )

        # Save general settings
        self.settings_manager.set_general_settings(
            remember_paths=self.remember_paths_var.get(),
            restore_window=self.restore_window_var.get(),
            confirm_delete=self.confirm_delete_var.get()
        )

        # Save advanced settings
        try:
            refresh_interval = int(self.refresh_interval_var.get())
        except ValueError:
            refresh_interval = 1000

        self.settings_manager.set_advanced_settings(
            refresh_interval=refresh_interval,
            debug_mode=self.debug_mode_var.get()
        )

        # Save current paths if remember_paths is enabled
        if self.remember_paths_var.get():
            self.settings_manager.save_current_paths(self.app.left_path, self.app.right_path)

        # Save window geometry if restore_window is enabled
        if self.restore_window_var.get():
            self.settings_manager.save_window_geometry(self.app.root.geometry())

        # Save to file
        self.settings_manager.save_settings()
    
    def reset_to_defaults(self):
        """Reset all settings to default values"""
        result = messagebox.askyesno(
            "Reset Settings",
            "Are you sure you want to reset all settings to their default values?",
            parent=self.dialog
        )
        if result:
            # Reset to defaults
            self.view_mode_var.set("Detailed List")
            self.show_preview_var.set(False)
            self.remember_paths_var.set(True)
            self.restore_window_var.set(True)
            self.confirm_delete_var.set(True)
            self.show_hidden_var.set(False)
            self.refresh_interval_var.set("1000")
            self.debug_mode_var.set(False)
            
            # Apply theme reset
            self.app.apply_theme('light')
            self.load_themes()
            
            self.on_setting_change()
