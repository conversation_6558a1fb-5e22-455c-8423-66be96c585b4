#!/usr/bin/env python3
"""
Test script to verify view mode and preview functionality
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_view_modes():
    """Test view mode functionality"""
    print("Testing view mode functionality...")
    
    try:
        from gexplorer.main import GExplorerApp
        
        # Create root window
        root = tk.Tk()
        root.title("GExplorer View Mode Test")
        root.geometry("1000x700")
        
        # Create app instance
        app = GExplorerApp(root)
        
        # Test that view mode components exist
        assert hasattr(app, 'view_mode'), "View mode variable not found"
        assert hasattr(app, 'left_file_view'), "Left file view not found"
        assert hasattr(app, 'right_file_view'), "Right file view not found"
        
        print("✓ View mode components exist")
        
        # Test view mode methods
        assert hasattr(app.left_file_view, 'set_view_mode'), "set_view_mode method not found"
        assert hasattr(app.left_file_view, 'view_mode'), "view_mode attribute not found"
        
        print("✓ View mode methods exist")
        
        # Test different view modes
        modes = ["Details", "List", "Icons"]
        for mode in modes:
            app.view_mode.set(mode)
            app.on_view_mode_change()
            
            left_mode = app.left_file_view.view_mode
            right_mode = app.right_file_view.view_mode
            
            print(f"✓ {mode} mode - Left: {left_mode}, Right: {right_mode}")
            assert left_mode == mode, f"Left view mode not set to {mode}"
            assert right_mode == mode, f"Right view mode not set to {mode}"
        
        print("✓ View mode switching test passed!")
        
        # Test preview functionality
        print("\nTesting preview functionality...")
        
        # Test preview toggle
        assert hasattr(app, 'show_preview'), "Preview toggle variable not found"
        assert hasattr(app, 'toggle_preview'), "Toggle preview method not found"
        
        print("✓ Preview components exist")
        
        # Test preview toggle
        initial_state = app.show_preview.get()
        app.show_preview.set(True)
        app.toggle_preview()
        
        preview_visible = app.preview_pane is not None
        print(f"✓ Preview toggle - Visible: {preview_visible}")
        
        # Toggle back
        app.show_preview.set(False)
        app.toggle_preview()
        
        preview_hidden = app.preview_pane is None
        print(f"✓ Preview toggle back - Hidden: {preview_hidden}")
        
        print("✓ Preview functionality test passed!")
        
        # Show window for manual testing
        print("\nShowing window for manual testing...")
        print("- Try changing view modes using the dropdown")
        print("- Toggle the Preview checkbox to test preview functionality")
        print("- Select files to see preview content")
        print("- Close the window when done testing")
        
        root.deiconify()  # Show the window
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ View mode/preview test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("GExplorer View Mode and Preview Test")
    print("=" * 50)
    
    success = test_view_modes()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 View mode and preview test completed!")
    else:
        print("❌ View mode/preview test failed.")
    
    sys.exit(0 if success else 1)
