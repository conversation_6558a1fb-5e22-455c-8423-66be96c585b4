import tkinter as tk
from tkinter import ttk
from ttkbootstrap import Style
from ttkbootstrap.constants import *
from gexplorer.ui.file_view import FileView
from gexplorer.ui.tree_view import TreeView
from gexplorer.filesystem.handler import list_directory
from gexplorer.logic.app_logic import AppLogic

class GExplorerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("GExplorer - Advanced File Explorer")
        self.root.geometry("800x600")
        
        # Initialize application logic
        self.app_logic = AppLogic(self.refresh_views)
        
        # Create main frame with paned window for split view
        self.paned_window = ttk.PanedWindow(self.root, orient=HORIZONTAL)
        self.paned_window.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # Left pane - Directory Tree
        self.left_frame = ttk.Frame(self.paned_window)
        self.paned_window.add(self.left_frame, weight=1)
        ttk.Label(self.left_frame, text="Directory Tree").pack()
        self.tree_view = TreeView(self.left_frame)
        self.tree_view.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # Right pane - File List
        self.right_frame = ttk.Frame(self.paned_window)
        self.paned_window.add(self.right_frame, weight=3)
        ttk.Label(self.right_frame, text="Files").pack()
        self.file_view = FileView(self.right_frame)
        self.file_view.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # Initialize current directory
        self.current_path = "."
        self.refresh_views()
        
        # Bind events for interaction
        self.tree_view.bind('<<TreeviewSelect>>', self.on_tree_select)
        self.file_view.bind('<Double-1>', self.on_file_double_click)
        
    def refresh_views(self):
        # Get directory contents
        items = list_directory(self.current_path)
        
        # Separate directories and files
        dirs = [item for item in items if item['type'] == 'directory']
        files = [item for item in items if item['type'] == 'file']
        
        # Populate views
        self.tree_view.populate(self.current_path, dirs)
        self.file_view.populate(files)
        
    def on_tree_select(self, event):
        selected_path = self.tree_view.get_selected_path()
        if selected_path and selected_path != self.current_path:
            self.current_path = selected_path
            self.refresh_views()
            
    def on_file_double_click(self, event):
        selected_file = self.file_view.get_selected_file()
        if selected_file:
            full_path = os.path.join(self.current_path, selected_file)
            self.app_logic.open_file(full_path)
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    style = Style(theme='darkly')
    root = style.master
    app = GExplorerApp(root)
    app.run()
