import tkinter as tk
from tkinter import ttk
import os
import sys
from datetime import datetime
from ttkbootstrap import Style
from ttkbootstrap.constants import *

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from gexplorer.ui.file_view import FileView
from gexplorer.filesystem.handler import list_directory
from gexplorer.logic.app_logic import AppLogic

class GExplorerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("GExplorer - Total Commander Style File Manager")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # Initialize application logic
        self.app_logic = AppLogic(self.refresh_views)

        # Initialize paths for dual panes
        self.left_path = os.getcwd()
        self.right_path = os.getcwd()
        self.active_pane = 'left'  # Track which pane is active

        # Configure custom styles
        self.configure_styles()

        self.create_ui()
        self.refresh_views()

    def configure_styles(self):
        """Configure custom styles for the application"""
        style = ttk.Style()

        # Configure toolbar styles
        style.configure('Toolbar.TFrame', background='#f0f0f0')
        style.configure('Toolbar.TButton',
                       padding=(8, 4),
                       font=('Segoe UI', 9))
        style.map('Toolbar.TButton',
                 background=[('active', '#e1e1e1'),
                           ('pressed', '#d0d0d0')])

        # Configure navigation button styles
        style.configure('Nav.TButton',
                       padding=(6, 3),
                       font=('Segoe UI', 10))
        style.map('Nav.TButton',
                 background=[('active', '#e1e1e1'),
                           ('pressed', '#d0d0d0')])

        # Configure breadcrumb button styles
        style.configure('Breadcrumb.TButton',
                       padding=(4, 2),
                       font=('Segoe UI', 9),
                       relief='flat')
        style.map('Breadcrumb.TButton',
                 background=[('active', '#e1e1e1'),
                           ('pressed', '#d0d0d0')])

        # Configure pane frame styles
        style.configure('Pane.TFrame',
                       background='white',
                       relief='solid',
                       borderwidth=1)

        # Configure status bar styles
        style.configure('Status.TFrame',
                       background='#f8f8f8',
                       relief='sunken',
                       borderwidth=1)
        style.configure('Status.TLabel',
                       background='#f8f8f8',
                       font=('Segoe UI', 8))

    def create_ui(self):
        """Create the main UI layout"""
        # Create main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # Create toolbar
        self.create_toolbar(main_frame)

        # Create dual-pane layout
        self.create_dual_panes(main_frame)

        # Create status bar
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """Create the toolbar with common operations"""
        toolbar_frame = ttk.Frame(parent, style='Toolbar.TFrame')
        toolbar_frame.pack(fill=X, pady=(0, 8))

        # File operations buttons with icons
        ttk.Button(toolbar_frame, text="📋 Copy", command=self.copy_files,
                  style='Toolbar.TButton').pack(side=LEFT, padx=3)
        ttk.Button(toolbar_frame, text="✂️ Move", command=self.move_files,
                  style='Toolbar.TButton').pack(side=LEFT, padx=3)
        ttk.Button(toolbar_frame, text="🗑️ Delete", command=self.delete_files,
                  style='Toolbar.TButton').pack(side=LEFT, padx=3)
        ttk.Button(toolbar_frame, text="📁 New Folder", command=self.create_folder,
                  style='Toolbar.TButton').pack(side=LEFT, padx=3)

        # Separator
        ttk.Separator(toolbar_frame, orient=VERTICAL).pack(side=LEFT, fill=Y, padx=8)

        # View options
        ttk.Button(toolbar_frame, text="🔄 Refresh", command=self.refresh_views,
                  style='Toolbar.TButton').pack(side=LEFT, padx=3)
        ttk.Button(toolbar_frame, text="ℹ️ Properties", command=self.show_properties,
                  style='Toolbar.TButton').pack(side=LEFT, padx=3)

        # Right side - View options
        view_frame = ttk.Frame(toolbar_frame)
        view_frame.pack(side=RIGHT, padx=5)

        # Preview toggle
        self.show_preview = tk.BooleanVar(value=False)
        ttk.Checkbutton(view_frame, text="Preview", variable=self.show_preview,
                       command=self.toggle_preview).pack(side=LEFT, padx=(0, 10))

        ttk.Label(view_frame, text="View:").pack(side=LEFT, padx=(0, 5))
        self.view_mode = tk.StringVar(value="Detailed List")
        view_combo = ttk.Combobox(view_frame, textvariable=self.view_mode,
                                 values=["Detailed List", "Simple List", "Large Icons"],
                                 state="readonly", width=12)
        view_combo.pack(side=LEFT)
        view_combo.bind('<<ComboboxSelected>>', self.on_view_mode_change)

    def create_dual_panes(self, parent):
        """Create the dual-pane file manager layout"""
        # Main container for panes and preview
        self.main_container = ttk.Frame(parent)
        self.main_container.pack(fill=BOTH, expand=True, pady=(0, 5))

        # Dual pane container
        self.paned_window = ttk.PanedWindow(self.main_container, orient=HORIZONTAL)
        self.paned_window.pack(fill=BOTH, expand=True, side=LEFT)

        # Left pane
        self.left_pane = self.create_file_pane(self.paned_window, 'left')
        self.paned_window.add(self.left_pane, weight=1)

        # Right pane
        self.right_pane = self.create_file_pane(self.paned_window, 'right')
        self.paned_window.add(self.right_pane, weight=1)

        # Preview pane (initially hidden)
        self.preview_pane = None

    def create_file_pane(self, parent, side):
        """Create a single file pane"""
        pane_frame = ttk.Frame(parent, style='Pane.TFrame')

        # Path navigation frame
        nav_frame = ttk.Frame(pane_frame)
        nav_frame.pack(fill=X, padx=8, pady=8)

        # Navigation container for breadcrumbs and path entry
        nav_container = ttk.Frame(nav_frame)
        nav_container.pack(fill=X, side=LEFT, expand=True)

        # Breadcrumb navigation
        breadcrumb_frame = ttk.Frame(nav_container)
        breadcrumb_frame.pack(fill=X, expand=True)

        # Path entry (hidden by default, shown when editing)
        path_var = tk.StringVar()
        path_entry = ttk.Entry(nav_container, textvariable=path_var)
        path_entry.pack_forget()  # Initially hidden

        # Navigation buttons
        button_frame = ttk.Frame(nav_frame)
        button_frame.pack(side=RIGHT)

        ttk.Button(button_frame, text="⬆️", width=4,
                  command=lambda: self.go_up(side),
                  style='Nav.TButton').pack(side=LEFT, padx=(5, 2))
        ttk.Button(button_frame, text="🏠", width=4,
                  command=lambda: self.go_home(side),
                  style='Nav.TButton').pack(side=LEFT, padx=2)
        ttk.Button(button_frame, text="🔄", width=4,
                  command=lambda: self.refresh_pane(side),
                  style='Nav.TButton').pack(side=LEFT, padx=2)

        # File list frame
        list_frame = ttk.Frame(pane_frame)
        list_frame.pack(fill=BOTH, expand=True, padx=8, pady=(0, 8))

        # Create file view
        file_view = FileView(list_frame)
        file_view.pack(fill=BOTH, expand=True)

        # Set up callbacks for the file view
        file_view.set_context_callback(lambda action, filename: self.handle_context_action(side, action, filename))
        file_view.set_keyboard_callback(lambda action, filename: self.handle_keyboard_action(side, action, filename))

        # Store references
        if side == 'left':
            self.left_path_var = path_var
            self.left_file_view = file_view
            self.left_breadcrumb_frame = breadcrumb_frame
            self.left_path_entry = path_entry
            path_var.set(self.left_path)
        else:
            self.right_path_var = path_var
            self.right_file_view = file_view
            self.right_breadcrumb_frame = breadcrumb_frame
            self.right_path_entry = path_entry
            path_var.set(self.right_path)

        # Bind events
        file_view.bind('<Button-1>', lambda e: self.set_active_pane(side))
        file_view.bind('<Double-1>', lambda e: self.on_file_double_click(side))
        file_view.bind('<<TreeviewSelect>>', lambda e: self.on_file_select(side))
        path_entry.bind('<Return>', lambda e: self.navigate_to_path(side))

        # Create initial breadcrumbs
        self.update_breadcrumbs(side)

        return pane_frame

    def create_status_bar(self, parent):
        """Create the status bar"""
        self.status_frame = ttk.Frame(parent, style='Status.TFrame')
        self.status_frame.pack(fill=X, pady=(5, 0))

        # Status labels
        self.status_left = ttk.Label(self.status_frame, text="Ready", style='Status.TLabel')
        self.status_left.pack(side=LEFT, padx=8, pady=4)

        self.status_right = ttk.Label(self.status_frame, text="", style='Status.TLabel')
        self.status_right.pack(side=RIGHT, padx=8, pady=4)

    def refresh_views(self):
        """Refresh both file panes"""
        self.refresh_pane('left')
        self.refresh_pane('right')

    def refresh_pane(self, side):
        """Refresh a specific pane"""
        path = self.left_path if side == 'left' else self.right_path
        file_view = self.left_file_view if side == 'left' else self.right_file_view
        path_var = self.left_path_var if side == 'left' else self.right_path_var

        try:
            # Get directory contents
            items = list_directory(path)
            files = [item for item in items if item['type'] == 'file']
            dirs = [item for item in items if item['type'] == 'directory']

            # Add parent directory entry if not at root
            if path != os.path.dirname(path):
                parent_entry = {
                    'name': '..',
                    'type': 'directory',
                    'size': 'N/A',
                    'last_modified': '0'
                }
                dirs.insert(0, parent_entry)

            # Combine directories and files
            all_items = dirs + files
            file_view.populate(all_items)
            path_var.set(path)

            # Update status and breadcrumbs
            self.update_status(side, len(dirs) - (1 if dirs and dirs[0]['name'] == '..' else 0), len(files))
            self.update_breadcrumbs(side)

        except Exception as e:
            self.status_left.config(text=f"Error: {str(e)}")

    def update_breadcrumbs(self, side):
        """Update the breadcrumb navigation for the specified pane"""
        breadcrumb_frame = self.left_breadcrumb_frame if side == 'left' else self.right_breadcrumb_frame
        current_path = self.left_path if side == 'left' else self.right_path

        # Clear existing breadcrumbs (all widgets except path entry)
        for widget in breadcrumb_frame.winfo_children():
            if not isinstance(widget, ttk.Entry):  # Keep the path entry
                widget.destroy()

        # Split path into components
        path_parts = []
        temp_path = current_path

        while temp_path and temp_path != os.path.dirname(temp_path):
            path_parts.append((os.path.basename(temp_path), temp_path))
            temp_path = os.path.dirname(temp_path)

        # Add root
        if temp_path:
            path_parts.append((temp_path, temp_path))

        path_parts.reverse()

        # Create breadcrumb buttons
        for i, (name, full_path) in enumerate(path_parts):
            if i > 0:
                ttk.Label(breadcrumb_frame, text=" > ").pack(side=LEFT)

            # Make the button text more readable
            display_name = name if name else full_path
            if len(display_name) > 15:
                display_name = display_name[:12] + "..."

            btn = ttk.Button(breadcrumb_frame, text=display_name,
                           command=lambda p=full_path: self.navigate_to_breadcrumb(side, p),
                           style='Breadcrumb.TButton')
            btn.pack(side=LEFT, padx=1)

        # Add edit button
        ttk.Button(breadcrumb_frame, text="✏️", width=4,
                  command=lambda: self.toggle_path_edit(side),
                  style='Nav.TButton').pack(side=LEFT, padx=(8, 0))

    def navigate_to_breadcrumb(self, side, path):
        """Navigate to a path from breadcrumb click"""
        if side == 'left':
            self.left_path = path
        else:
            self.right_path = path
        self.refresh_pane(side)

    def toggle_path_edit(self, side):
        """Toggle between breadcrumb and path entry modes"""
        breadcrumb_frame = self.left_breadcrumb_frame if side == 'left' else self.right_breadcrumb_frame
        path_entry = self.left_path_entry if side == 'left' else self.right_path_entry
        path_var = self.left_path_var if side == 'left' else self.right_path_var
        current_path = self.left_path if side == 'left' else self.right_path

        if path_entry.winfo_viewable():
            # Switch back to breadcrumbs
            path_entry.pack_forget()
            breadcrumb_frame.pack(fill=X, expand=True)
            self.update_breadcrumbs(side)
        else:
            # Switch to path entry
            breadcrumb_frame.pack_forget()
            path_entry.pack(fill=X, expand=True)
            path_var.set(current_path)
            path_entry.focus_set()
            path_entry.select_range(0, tk.END)

    def set_active_pane(self, side):
        """Set the active pane"""
        self.active_pane = side

    def navigate_to_path(self, side):
        """Navigate to the path entered in the path bar"""
        path_var = self.left_path_var if side == 'left' else self.right_path_var
        new_path = path_var.get()

        if os.path.exists(new_path) and os.path.isdir(new_path):
            if side == 'left':
                self.left_path = os.path.abspath(new_path)
            else:
                self.right_path = os.path.abspath(new_path)
            self.refresh_pane(side)
            # Switch back to breadcrumb mode
            self.toggle_path_edit(side)
        else:
            # Reset to current path if invalid
            current_path = self.left_path if side == 'left' else self.right_path
            path_var.set(current_path)

    def go_up(self, side):
        """Navigate to parent directory"""
        current_path = self.left_path if side == 'left' else self.right_path
        parent_path = os.path.dirname(current_path)

        if parent_path != current_path:  # Not at root
            if side == 'left':
                self.left_path = parent_path
            else:
                self.right_path = parent_path
            self.refresh_pane(side)

    def go_home(self, side):
        """Navigate to home directory"""
        home_path = os.path.expanduser("~")
        if side == 'left':
            self.left_path = home_path
        else:
            self.right_path = home_path
        self.refresh_pane(side)

    def on_file_double_click(self, side):
        """Handle double-click on file or directory"""
        file_view = self.left_file_view if side == 'left' else self.right_file_view
        selected_file = file_view.get_selected_file()

        if selected_file:
            current_path = self.left_path if side == 'left' else self.right_path

            if selected_file == '..':
                # Navigate to parent directory
                self.go_up(side)
            else:
                full_path = os.path.join(current_path, selected_file)

                if os.path.isdir(full_path):
                    # Navigate into directory
                    if side == 'left':
                        self.left_path = full_path
                    else:
                        self.right_path = full_path
                    self.refresh_pane(side)
                else:
                    # Open file
                    self.app_logic.open_file(full_path)

    def handle_context_action(self, side, action, filename):
        """Handle context menu actions"""
        if action == "open":
            self.on_file_double_click(side)
        elif action == "go_up":
            self.go_up(side)
        elif action == "copy":
            self.copy_selected_file(side)
        elif action == "cut":
            self.cut_selected_file(side)
        elif action == "delete":
            self.delete_selected_file(side)
        elif action == "rename":
            self.rename_selected_file(side)
        elif action == "properties":
            self.show_file_properties(side)

    def handle_keyboard_action(self, side, action, filename):
        """Handle keyboard shortcuts"""
        if action == "open":
            self.on_file_double_click(side)
        elif action == "go_up":
            self.go_up(side)
        elif action == "copy":
            self.copy_selected_file(side)
        elif action == "cut":
            self.cut_selected_file(side)
        elif action == "paste":
            self.paste_files(side)
        elif action == "delete":
            self.delete_selected_file(side)
        elif action == "rename":
            self.rename_selected_file(side)

    def copy_selected_file(self, side):
        """Copy the selected file from the specified pane"""
        file_view = self.left_file_view if side == 'left' else self.right_file_view
        selected_file = file_view.get_selected_file()

        if selected_file and selected_file != '..':
            # Store for clipboard functionality
            current_path = self.left_path if side == 'left' else self.right_path
            self.clipboard_file = os.path.join(current_path, selected_file)
            self.clipboard_action = 'copy'
            self.status_left.config(text=f"Copied: {selected_file}")

    def cut_selected_file(self, side):
        """Cut the selected file from the specified pane"""
        file_view = self.left_file_view if side == 'left' else self.right_file_view
        selected_file = file_view.get_selected_file()

        if selected_file and selected_file != '..':
            # Store for clipboard functionality
            current_path = self.left_path if side == 'left' else self.right_path
            self.clipboard_file = os.path.join(current_path, selected_file)
            self.clipboard_action = 'cut'
            self.status_left.config(text=f"Cut: {selected_file}")

    def paste_files(self, side):
        """Paste files to the specified pane"""
        if hasattr(self, 'clipboard_file') and self.clipboard_file:
            dest_path = self.left_path if side == 'left' else self.right_path
            filename = os.path.basename(self.clipboard_file)
            dest_full_path = os.path.join(dest_path, filename)

            try:
                if self.clipboard_action == 'copy':
                    self.app_logic.copy_file(self.clipboard_file, dest_full_path)
                    self.status_left.config(text=f"Pasted: {filename}")
                elif self.clipboard_action == 'cut':
                    self.app_logic.move_file(self.clipboard_file, dest_full_path)
                    self.clipboard_file = None  # Clear after move
                    self.status_left.config(text=f"Moved: {filename}")

                self.refresh_views()
            except Exception as e:
                self.status_left.config(text=f"Paste failed: {str(e)}")

    def delete_selected_file(self, side):
        """Delete the selected file from the specified pane"""
        file_view = self.left_file_view if side == 'left' else self.right_file_view
        selected_file = file_view.get_selected_file()

        if selected_file and selected_file != '..':
            current_path = self.left_path if side == 'left' else self.right_path
            full_path = os.path.join(current_path, selected_file)

            # Confirm deletion
            from tkinter import messagebox
            if messagebox.askyesno("Confirm Delete", f"Delete '{selected_file}'?"):
                try:
                    self.app_logic.delete_file(full_path)
                    self.refresh_views()
                    self.status_left.config(text=f"Deleted: {selected_file}")
                except Exception as e:
                    self.status_left.config(text=f"Delete failed: {str(e)}")

    def rename_selected_file(self, side):
        """Rename the selected file from the specified pane"""
        file_view = self.left_file_view if side == 'left' else self.right_file_view
        selected_file = file_view.get_selected_file()

        if selected_file and selected_file != '..':
            from tkinter import simpledialog
            new_name = simpledialog.askstring("Rename", f"New name for '{selected_file}':", initialvalue=selected_file)

            if new_name and new_name != selected_file:
                current_path = self.left_path if side == 'left' else self.right_path
                old_path = os.path.join(current_path, selected_file)
                new_path = os.path.join(current_path, new_name)

                try:
                    self.app_logic.rename_file(old_path, new_name)
                    self.refresh_views()
                    self.status_left.config(text=f"Renamed: {selected_file} → {new_name}")
                except Exception as e:
                    self.status_left.config(text=f"Rename failed: {str(e)}")

    def show_file_properties(self, side):
        """Show detailed properties of the selected file"""
        file_view = self.left_file_view if side == 'left' else self.right_file_view
        selected_file = file_view.get_selected_file()

        if selected_file and selected_file != '..':
            current_path = self.left_path if side == 'left' else self.right_path
            file_path = os.path.join(current_path, selected_file)

            try:
                self.show_properties_dialog(file_path, selected_file)
            except Exception as e:
                self.status_left.config(text=f"Properties error: {str(e)}")

    def show_properties_dialog(self, file_path, filename):
        """Show a detailed properties dialog"""
        import tkinter as tk
        from tkinter import ttk
        import stat

        # Create properties window
        props_window = tk.Toplevel(self.root)
        props_window.title(f"Properties - {filename}")
        props_window.geometry("450x350")
        props_window.resizable(False, False)
        props_window.transient(self.root)
        props_window.grab_set()

        # Center the window
        props_window.update_idletasks()
        x = (props_window.winfo_screenwidth() // 2) - (450 // 2)
        y = (props_window.winfo_screenheight() // 2) - (350 // 2)
        props_window.geometry(f"450x350+{x}+{y}")

        # Create notebook for tabs
        notebook = ttk.Notebook(props_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # General tab
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="General")

        try:
            file_stat = os.stat(file_path)

            # File icon and name
            info_frame = ttk.Frame(general_frame)
            info_frame.pack(fill=tk.X, padx=10, pady=10)

            # Get file icon based on type
            if os.path.isdir(file_path):
                icon = "📁"
                file_type = "Folder"
            else:
                ext = filename.split('.')[-1].lower() if '.' in filename else ''
                icon = self.get_file_icon_for_props(ext)
                file_type = f"{ext.upper()} File" if ext else "File"

            ttk.Label(info_frame, text=icon, font=('Segoe UI', 24)).pack(side=tk.LEFT, padx=(0, 10))

            name_frame = ttk.Frame(info_frame)
            name_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            ttk.Label(name_frame, text=filename, font=('Segoe UI', 12, 'bold')).pack(anchor=tk.W)
            ttk.Label(name_frame, text=file_type, font=('Segoe UI', 9)).pack(anchor=tk.W)

            # Properties
            props_frame = ttk.LabelFrame(general_frame, text="Properties", padding=10)
            props_frame.pack(fill=tk.X, padx=10, pady=10)

            # Size
            if os.path.isfile(file_path):
                size = file_stat.st_size
                size_str = self.format_file_size(size)
                self.add_property_row(props_frame, "Size:", f"{size_str} ({size:,} bytes)")

            # Location
            self.add_property_row(props_frame, "Location:", os.path.dirname(file_path))

            # Dates
            created = datetime.fromtimestamp(file_stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S')
            modified = datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            accessed = datetime.fromtimestamp(file_stat.st_atime).strftime('%Y-%m-%d %H:%M:%S')

            self.add_property_row(props_frame, "Created:", created)
            self.add_property_row(props_frame, "Modified:", modified)
            self.add_property_row(props_frame, "Accessed:", accessed)

            # Attributes
            attrs_frame = ttk.LabelFrame(general_frame, text="Attributes", padding=10)
            attrs_frame.pack(fill=tk.X, padx=10, pady=10)

            mode = file_stat.st_mode
            attrs = []
            if stat.S_ISDIR(mode):
                attrs.append("Directory")
            if mode & stat.S_IREAD:
                attrs.append("Readable")
            if mode & stat.S_IWRITE:
                attrs.append("Writable")
            if mode & stat.S_IEXEC:
                attrs.append("Executable")
            if filename.startswith('.'):
                attrs.append("Hidden")

            ttk.Label(attrs_frame, text=", ".join(attrs)).pack(anchor=tk.W)

        except Exception as e:
            ttk.Label(general_frame, text=f"Error reading file properties: {str(e)}").pack(padx=10, pady=10)

        # Close button
        button_frame = ttk.Frame(props_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="Close", command=props_window.destroy).pack(side=tk.RIGHT)

    def add_property_row(self, parent, label, value):
        """Add a property row to the properties dialog"""
        row_frame = ttk.Frame(parent)
        row_frame.pack(fill=tk.X, pady=2)

        ttk.Label(row_frame, text=label, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT, anchor=tk.W)
        ttk.Label(row_frame, text=value, font=('Segoe UI', 9)).pack(side=tk.LEFT, padx=(10, 0), anchor=tk.W)

    def get_file_icon_for_props(self, extension):
        """Get file icon for properties dialog"""
        icon_map = {
            'txt': '📄', 'doc': '📄', 'docx': '📄', 'pdf': '📕',
            'xls': '📊', 'xlsx': '📊', 'csv': '📊',
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
            'mp4': '🎬', 'avi': '🎬', 'mkv': '🎬',
            'mp3': '🎵', 'wav': '🎵', 'flac': '🎵',
            'zip': '📦', 'rar': '📦', '7z': '📦',
            'py': '🐍', 'js': '📜', 'html': '🌐', 'css': '🎨',
            'exe': '⚙️', 'msi': '⚙️',
        }
        return icon_map.get(extension, '📄')

    def format_file_size(self, size):
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def toggle_preview(self):
        """Toggle the preview pane"""
        if self.show_preview.get():
            self.create_preview_pane()
            # Trigger preview update for currently selected file
            active_side = self.active_pane
            self.on_file_select(active_side)
        else:
            self.hide_preview_pane()

    def create_preview_pane(self):
        """Create and show the preview pane"""
        if self.preview_pane is None:
            # Reconfigure main container to use horizontal paned window
            self.paned_window.pack_forget()

            # Create main horizontal paned window
            self.main_paned = ttk.PanedWindow(self.main_container, orient=HORIZONTAL)
            self.main_paned.pack(fill=BOTH, expand=True)

            # Add the dual pane window to the left side
            self.main_paned.add(self.paned_window, weight=3)

            # Create preview pane
            self.preview_pane = ttk.Frame(self.main_paned, style='Pane.TFrame')
            self.main_paned.add(self.preview_pane, weight=1)

            # Preview content
            preview_label = ttk.Label(self.preview_pane, text="Preview",
                                    font=('Segoe UI', 10, 'bold'))
            preview_label.pack(pady=10)

            # Preview content frame
            content_frame = ttk.Frame(self.preview_pane)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Preview text widget with proper layout
            self.preview_text = tk.Text(content_frame, wrap=tk.WORD,
                                      font=('Consolas', 9), state=tk.DISABLED)
            preview_scroll = ttk.Scrollbar(content_frame, orient=tk.VERTICAL,
                                         command=self.preview_text.yview)
            self.preview_text.configure(yscrollcommand=preview_scroll.set)

            preview_scroll.pack(side=tk.RIGHT, fill=tk.Y)
            self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # Image preview label (initially hidden)
            self.preview_image_label = ttk.Label(content_frame)
            self.preview_image_label.pack_forget()  # Initially hidden

    def hide_preview_pane(self):
        """Hide the preview pane"""
        if self.preview_pane is not None:
            self.main_paned.destroy()
            self.preview_pane = None
            self.paned_window.pack(fill=BOTH, expand=True, side=LEFT)

    def on_file_select(self, side):
        """Handle file selection for preview"""
        # Always update preview if enabled, even if preview pane doesn't exist yet
        if self.show_preview.get():
            # Create preview pane if it doesn't exist
            if self.preview_pane is None:
                self.create_preview_pane()

            file_view = self.left_file_view if side == 'left' else self.right_file_view
            selected_file = file_view.get_selected_file()

            if selected_file and selected_file != '..':
                current_path = self.left_path if side == 'left' else self.right_path
                file_path = os.path.join(current_path, selected_file)
                self.update_preview(file_path)
            elif self.preview_pane is not None:
                # Clear preview if no valid file selected
                self.preview_text.config(state=tk.NORMAL)
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(tk.END, "Select a file to preview")
                self.preview_text.config(state=tk.DISABLED)

    def update_preview(self, file_path):
        """Update the preview pane with file content"""
        if not hasattr(self, 'preview_text'):
            return

        # Hide image preview and show text preview by default
        if hasattr(self, 'preview_image_label'):
            self.preview_image_label.pack_forget()
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.preview_text.config(state=tk.NORMAL)
        self.preview_text.delete(1.0, tk.END)

        try:
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                ext = file_path.split('.')[-1].lower() if '.' in file_path else ''

                # Check if it's an image file
                if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
                    self.show_image_preview(file_path, file_size)
                    return

                # Only preview text files under 1MB
                if file_size > 1024 * 1024:
                    self.preview_text.insert(tk.END, f"File too large to preview\nSize: {self.format_file_size(file_size)}")
                else:
                    # Try to determine if it's a text file
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read(10000)  # Read first 10KB
                            if len(content) == 10000:
                                content += "\n\n... (truncated)"
                            self.preview_text.insert(tk.END, content)
                    except Exception:
                        # Binary file
                        self.preview_text.insert(tk.END, f"Binary file: {os.path.basename(file_path)}\n")
                        self.preview_text.insert(tk.END, f"Size: {self.format_file_size(file_size)}\n")
                        self.preview_text.insert(tk.END, "Binary preview not supported.")
            else:
                self.preview_text.insert(tk.END, f"Directory: {os.path.basename(file_path)}")

        except Exception as e:
            self.preview_text.insert(tk.END, f"Error previewing file: {str(e)}")

        self.preview_text.config(state=tk.DISABLED)

    def show_image_preview(self, file_path, file_size):
        """Show image preview"""
        try:
            from PIL import Image, ImageTk

            # Hide text preview and show image preview
            self.preview_text.pack_forget()
            if hasattr(self, 'preview_image_label'):
                self.preview_image_label.pack(fill=tk.BOTH, expand=True)

            # Load and resize image
            image = Image.open(file_path)

            # Calculate size to fit in preview pane (max 300x300)
            max_size = 300
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(image)

            # Update image label
            self.preview_image_label.configure(image=photo, text="")
            self.preview_image_label.image = photo  # Keep a reference

            # Show image info in text widget
            self.preview_text.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)

            original_size = Image.open(file_path).size
            self.preview_text.insert(tk.END, f"Image: {os.path.basename(file_path)}\n")
            self.preview_text.insert(tk.END, f"Size: {self.format_file_size(file_size)}\n")
            self.preview_text.insert(tk.END, f"Dimensions: {original_size[0]} x {original_size[1]} pixels")
            self.preview_text.config(state=tk.DISABLED)

        except ImportError:
            # PIL not available, show text info
            self.preview_text.insert(tk.END, f"Image file: {os.path.basename(file_path)}\n")
            self.preview_text.insert(tk.END, f"Size: {self.format_file_size(file_size)}\n")
            self.preview_text.insert(tk.END, "Install Pillow (PIL) for image preview support.")
        except Exception as e:
            self.preview_text.insert(tk.END, f"Error loading image: {str(e)}")

    def on_view_mode_change(self, event=None):
        """Handle view mode changes"""
        display_mode = self.view_mode.get()

        # Map display names to internal mode names
        mode_mapping = {
            "Detailed List": "Details",
            "Simple List": "List",
            "Large Icons": "Icons"
        }

        internal_mode = mode_mapping.get(display_mode, "Details")

        # Apply view mode to both file views
        self.left_file_view.set_view_mode(internal_mode)
        self.right_file_view.set_view_mode(internal_mode)

        # Refresh both panes to apply the new view mode
        self.refresh_pane('left')
        self.refresh_pane('right')

    def copy_files(self):
        """Copy selected files from active pane to inactive pane"""
        source_view = self.left_file_view if self.active_pane == 'left' else self.right_file_view
        source_path = self.left_path if self.active_pane == 'left' else self.right_path
        dest_path = self.right_path if self.active_pane == 'left' else self.left_path

        selected_file = source_view.get_selected_file()
        if selected_file and selected_file != '..':
            source_full_path = os.path.join(source_path, selected_file)
            dest_full_path = os.path.join(dest_path, selected_file)

            try:
                self.app_logic.copy_file(source_full_path, dest_full_path)
                self.refresh_views()
                self.status_left.config(text=f"Copied: {selected_file}")
            except Exception as e:
                self.status_left.config(text=f"Copy failed: {str(e)}")

    def move_files(self):
        """Move selected files from active pane to inactive pane"""
        source_view = self.left_file_view if self.active_pane == 'left' else self.right_file_view
        source_path = self.left_path if self.active_pane == 'left' else self.right_path
        dest_path = self.right_path if self.active_pane == 'left' else self.left_path

        selected_file = source_view.get_selected_file()
        if selected_file and selected_file != '..':
            source_full_path = os.path.join(source_path, selected_file)
            dest_full_path = os.path.join(dest_path, selected_file)

            try:
                self.app_logic.move_file(source_full_path, dest_full_path)
                self.refresh_views()
                self.status_left.config(text=f"Moved: {selected_file}")
            except Exception as e:
                self.status_left.config(text=f"Move failed: {str(e)}")

    def delete_files(self):
        """Delete selected files from active pane"""
        source_view = self.left_file_view if self.active_pane == 'left' else self.right_file_view
        source_path = self.left_path if self.active_pane == 'left' else self.right_path

        selected_file = source_view.get_selected_file()
        if selected_file and selected_file != '..':
            source_full_path = os.path.join(source_path, selected_file)

            # Confirm deletion
            from tkinter import messagebox
            if messagebox.askyesno("Confirm Delete", f"Delete '{selected_file}'?"):
                try:
                    self.app_logic.delete_file(source_full_path)
                    self.refresh_views()
                    self.status_left.config(text=f"Deleted: {selected_file}")
                except Exception as e:
                    self.status_left.config(text=f"Delete failed: {str(e)}")

    def create_folder(self):
        """Create a new folder in the active pane"""
        from tkinter import simpledialog

        folder_name = simpledialog.askstring("New Folder", "Enter folder name:")
        if folder_name:
            current_path = self.left_path if self.active_pane == 'left' else self.right_path
            new_folder_path = os.path.join(current_path, folder_name)

            try:
                os.makedirs(new_folder_path, exist_ok=False)
                self.refresh_views()
                self.status_left.config(text=f"Created folder: {folder_name}")
            except Exception as e:
                self.status_left.config(text=f"Create folder failed: {str(e)}")

    def show_properties(self):
        """Show properties of selected file"""
        source_view = self.left_file_view if self.active_pane == 'left' else self.right_file_view
        source_path = self.left_path if self.active_pane == 'left' else self.right_path

        selected_file = source_view.get_selected_file()
        if selected_file and selected_file != '..':
            file_path = os.path.join(source_path, selected_file)
            self.show_properties_dialog(file_path, selected_file)
        else:
            self.status_left.config(text="No file selected for properties")

    def update_status(self, side, dirs_count, files_count):
        """Update status bar with file counts"""
        status_text = f"{dirs_count} dirs, {files_count} files"
        if side == 'left':
            self.status_left.config(text=status_text)
        else:
            self.status_right.config(text=status_text)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    style = Style(theme='darkly')
    root = style.master
    app = GExplorerApp(root)
    app.run()
