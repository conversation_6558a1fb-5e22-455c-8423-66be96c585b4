import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *
from typing import List, Dict, Optional, Callable
import os
from pathlib import Path

class TreeView(ttk.Treeview):
    """
    A widget to display a hierarchical directory tree structure for Windows Explorer style navigation.
    """
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # Configure tree view for folder navigation
        self['columns'] = ()  # No additional columns, just the tree
        self.column('#0', width=250, minwidth=150)
        self.heading('#0', text='Folders', anchor='w')

        # Callbacks
        self.selection_callback: Optional[Callable] = None

        # Track expanded nodes to avoid re-expansion
        self.expanded_paths: set = set()

        # Bind events
        self.bind('<<TreeviewSelect>>', self.on_select)
        self.bind('<<TreeviewOpen>>', self.on_expand)

        self._selected_path = ''

        # Initialize with root drives/directories
        self.populate_root()

    def set_selection_callback(self, callback: Callable[[str], None]):
        """Set callback for when a folder is selected"""
        self.selection_callback = callback

    def populate_root(self):
        """Populate the tree with root drives/directories"""
        # Clear existing items
        for item in self.get_children():
            self.delete(item)

        # On Windows, show drives; on Unix, show root and common directories
        if os.name == 'nt':
            # Windows - show drives
            import string
            for drive in string.ascii_uppercase:
                drive_path = f"{drive}:\\"
                if os.path.exists(drive_path):
                    drive_name = f"{drive}: Drive"
                    try:
                        # Check if we can access the drive
                        os.listdir(drive_path)
                        item_id = self.insert('', 'end', text=drive_name,
                                            values=(drive_path,), open=False)
                        # Add a dummy child to show the expand arrow
                        self.insert(item_id, 'end', text='Loading...', values=('',))
                    except (PermissionError, OSError):
                        # Drive not accessible
                        continue
        else:
            # Unix-like systems
            root_path = "/"
            if os.path.exists(root_path):
                item_id = self.insert('', 'end', text='/', values=(root_path,), open=False)
                self.insert(item_id, 'end', text='Loading...', values=('',))

            # Add common directories
            home_path = str(Path.home())
            if os.path.exists(home_path):
                item_id = self.insert('', 'end', text='Home', values=(home_path,), open=False)
                self.insert(item_id, 'end', text='Loading...', values=('',))

    def on_select(self, event):
        """Handle tree selection"""
        selection = self.selection()
        if selection and self.selection_callback:
            item = selection[0]
            values = self.item(item, 'values')
            if values and values[0]:
                folder_path = values[0]
                self._selected_path = folder_path
                self.selection_callback(folder_path)

    def on_expand(self, event):
        """Handle tree expansion - populate child directories"""
        selection = self.selection()
        if not selection:
            return

        item = selection[0]
        values = self.item(item, 'values')
        if not values or not values[0]:
            return

        folder_path = values[0]

        # Check if already populated
        if folder_path in self.expanded_paths:
            return

        # Remove dummy child
        children = self.get_children(item)
        for child in children:
            child_values = self.item(child, 'values')
            if not child_values or not child_values[0]:  # Dummy item
                self.delete(child)

        # Populate with actual subdirectories
        self.populate_subdirectories(item, folder_path)
        self.expanded_paths.add(folder_path)

    def populate_subdirectories(self, parent_item: str, folder_path: str):
        """Populate subdirectories for a given folder"""
        try:
            # Get all subdirectories
            subdirs = []
            for entry in os.listdir(folder_path):
                entry_path = os.path.join(folder_path, entry)
                if os.path.isdir(entry_path):
                    # Skip hidden directories (starting with .)
                    if not entry.startswith('.'):
                        subdirs.append((entry, entry_path))

            # Sort directories alphabetically
            subdirs.sort(key=lambda x: x[0].lower())

            # Add subdirectories to tree
            for dir_name, dir_path in subdirs:
                try:
                    # Check if directory is accessible
                    os.listdir(dir_path)
                    item_id = self.insert(parent_item, 'end', text=dir_name,
                                        values=(dir_path,), open=False)

                    # Check if this directory has subdirectories
                    has_subdirs = self.has_subdirectories(dir_path)
                    if has_subdirs:
                        # Add dummy child to show expand arrow
                        self.insert(item_id, 'end', text='Loading...', values=('',))

                except (PermissionError, OSError):
                    # Directory not accessible, skip
                    continue

        except (PermissionError, OSError):
            # Cannot access parent directory
            pass

    def has_subdirectories(self, folder_path: str) -> bool:
        """Check if a folder has any subdirectories"""
        try:
            for entry in os.listdir(folder_path):
                entry_path = os.path.join(folder_path, entry)
                if os.path.isdir(entry_path) and not entry.startswith('.'):
                    return True
            return False
        except (PermissionError, OSError):
            return False

    def select_path(self, target_path: str):
        """Select and expand to a specific path"""
        # Normalize the path
        target_path = os.path.normpath(target_path)

        # Find and expand the path in the tree
        self.expand_to_path(target_path)

    def expand_to_path(self, target_path: str):
        """Expand the tree to show a specific path"""
        # This is a simplified implementation
        # In a full implementation, you would traverse the tree structure
        # and expand nodes as needed to reach the target path
        pass

    def get_selected_path(self) -> str:
        """
        Get the path of the currently selected directory.

        Returns:
            str: Path of the selected directory, or empty string if no selection.
        """
        return self._selected_path
