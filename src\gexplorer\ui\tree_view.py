import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *
from typing import List, Dict, Optional
import os

class TreeView(ttk.Treeview):
    """
    A widget to display a hierarchical directory tree structure.
    """
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Define columns for the tree view
        self['columns'] = ('name',)
        self.column('#0', width=0, stretch=False)  # Hide the default first column
        self.column('name', anchor=W, width=250, minwidth=150)
        
        # Define headings
        self.heading('name', text='Directories', anchor=W)
        
        # Add a scrollbar
        scrollbar = ttk.Scrollbar(parent, orient=VERTICAL, command=self.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.configure(yscrollcommand=scrollbar.set)
        
        # Horizontal scrollbar
        h_scrollbar = ttk.Scrollbar(parent, orient=HORIZONTAL, command=self.xview)
        h_scrollbar.pack(side=BOTTOM, fill=X)
        self.configure(xscrollcommand=h_scrollbar.set)
        
        # Bind selection event
        self.bind('<<TreeviewSelect>>', self.on_select)
        
        self._selected_path = ''
        
    def populate(self, root_path: str, items: List[Dict[str, str]]):
        """
        Populate the directory tree with data starting from a root path.
        
        Args:
            root_path (str): The root directory path to start the tree from.
            items (List[Dict[str, str]]): List of dictionaries containing directory information.
        """
        # Clear existing items
        for item in self.get_children():
            self.delete(item)
            
        # Add root node
        root_node = self.insert('', END, values=(os.path.basename(root_path) or root_path,))
        self._populate_recursive(root_node, root_path, items)
        
    def _populate_recursive(self, parent_node: str, parent_path: str, items: List[Dict[str, str]]):
        """
        Recursively populate the tree with directory structure.
        
        Args:
            parent_node (str): The parent node ID in the tree.
            parent_path (str): The file system path of the parent node.
            items (List[Dict[str, str]]): List of dictionaries containing item information.
        """
        for item in items:
            if item['type'] == 'directory':
                item_path = item['path']
                node = self.insert(parent_node, END, values=(item['name'],))
                # Placeholder for child nodes (will be populated on expansion if needed)
                self.insert(node, END, values=('Loading...',))
                
    def on_select(self, event):
        """
        Handle selection of a node in the tree.
        """
        selection = self.selection()
        if selection:
            item = self.item(selection[0])
            self._selected_path = item['values'][0] if item['values'] else ''
            
    def get_selected_path(self) -> str:
        """
        Get the path of the currently selected directory.
        
        Returns:
            str: Path of the selected directory, or empty string if no selection.
        """
        return self._selected_path
