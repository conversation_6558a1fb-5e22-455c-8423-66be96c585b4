#!/usr/bin/env python3
"""
Test script for the theme system in GExplorer
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ttkbootstrap import Style
from gexplorer.main import GExplorerApp

def test_theme_system():
    """Test the theme system functionality"""
    print("🎨 Testing GExplorer Theme System")
    print("=" * 50)
    
    # Test theme manager
    from gexplorer.themes import ThemeManager
    theme_manager = ThemeManager()
    
    print("📋 Available Themes:")
    themes = theme_manager.get_available_themes()
    for theme_id, theme_name in themes.items():
        print(f"  - {theme_id}: {theme_name}")
    
    print(f"\n🔍 Testing theme data loading...")
    for theme_id in themes.keys():
        try:
            theme_data = theme_manager.get_theme(theme_id)
            colors = theme_data.get('colors', {})
            fonts = theme_data.get('fonts', {})
            base_theme = theme_data.get('base_theme', 'unknown')
            
            print(f"  ✓ {theme_id}: {len(colors)} colors, {len(fonts)} fonts, base: {base_theme}")
        except Exception as e:
            print(f"  ❌ {theme_id}: Error - {e}")
    
    print(f"\n🚀 Starting GExplorer with theme system...")
    print("Instructions for testing:")
    print("1. Click the '🎨 Themes' button in the toolbar")
    print("2. Try different themes from the list:")
    print("   - Light Theme (clean, bright)")
    print("   - Dark Theme (dark background, light text)")
    print("   - Blue Theme (blue accents)")
    print("   - Green Theme (green accents)")
    print("3. Test 'Create Custom' to make your own theme")
    print("4. Try editing colors by clicking the color buttons")
    print("5. Test 'Apply Theme' to see changes immediately")
    print("6. Notice how the contrast is now proper!")
    print("\n🎯 Key improvements:")
    print("- No more white text on white background!")
    print("- Consistent color schemes")
    print("- Proper contrast ratios")
    print("- Theme persistence (saves your choice)")
    print("- Custom theme creation and editing")
    
    # Start the application
    style = Style(theme='flatly')  # Start with light theme
    root = style.master
    app = GExplorerApp(root, style)
    
    print(f"\n🎨 Current theme: {app.current_theme_id}")
    print("Close the window when done testing.")
    
    app.run()

if __name__ == "__main__":
    test_theme_system()
