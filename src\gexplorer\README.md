# GExplorer
An advanced desktop file explorer built with Python and Tkinter.

## Overview
GExplorer aims to provide innovative features beyond standard file explorers, enhancing user productivity with a modern interface and powerful functionalities.

## Structure
- `ui/`: User interface components using Tkinter and ttkbootstrap.
- `filesystem/`: File system operations with os, pathlib, and shutil.
- `logic/`: Application logic coordinating UI and filesystem.
- `utils/`: Utility functions and helpers.
