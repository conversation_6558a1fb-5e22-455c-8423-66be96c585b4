"""
Settings management for GExplorer
Handles loading, saving, and applying all configuration options
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path


class SettingsManager:
    """Manages application settings with persistence"""
    
    def __init__(self):
        self.settings_dir = Path.home() / '.gexplorer'
        self.settings_file = self.settings_dir / 'settings.json'
        self.ensure_settings_dir()
        
        # Default settings
        self.default_settings = {
            'theme': {
                'current_theme_id': 'light',
                'custom_themes': {}
            },
            'view': {
                'default_view_mode': 'Detailed List',
                'show_preview': False,
                'show_hidden_files': False,
                'column_widths': {
                    'name': 250,
                    'type': 80,
                    'size': 100,
                    'last_modified': 150
                }
            },
            'layout': {
                'mode': 'total_commander',  # 'total_commander' or 'windows_explorer'
                'pane_orientation': 'horizontal',  # 'horizontal' or 'vertical'
                'toolbar_position': 'top',  # 'top', 'bottom', 'left', 'right', 'hidden'
                'status_bar_visible': True,
                'breadcrumb_style': 'buttons',  # 'buttons' or 'text'
                'show_tree_view': False,  # For Windows Explorer mode
                'pane_split_ratio': 0.5,  # Ratio for dual pane split
                'preview_position': 'right',  # 'right', 'bottom', 'floating'
                'show_toolbar_text': True,
                'toolbar_icon_size': 'medium'  # 'small', 'medium', 'large'
            },
            'general': {
                'remember_paths': True,
                'restore_window': True,
                'confirm_delete': True,
                'startup_paths': {
                    'left': '',
                    'right': ''
                }
            },
            'window': {
                'geometry': '1200x800',
                'position': 'center',
                'maximized': False,
                'active_pane': 'left'
            },
            'advanced': {
                'refresh_interval': 1000,
                'debug_mode': False,
                'auto_refresh': True,
                'file_associations': {}
            }
        }
        
        self.current_settings = self.load_settings()
    
    def ensure_settings_dir(self):
        """Ensure the settings directory exists"""
        self.settings_dir.mkdir(exist_ok=True)
    
    def load_settings(self) -> Dict[str, Any]:
        """Load settings from file or return defaults"""
        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # Merge with defaults to ensure all keys exist
                return self.merge_settings(self.default_settings, loaded_settings)
                
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading settings: {e}")
                return self.default_settings.copy()
        else:
            return self.default_settings.copy()
    
    def save_settings(self) -> bool:
        """Save current settings to file"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, indent=2, ensure_ascii=False)
            return True
        except IOError as e:
            print(f"Error saving settings: {e}")
            return False
    
    def merge_settings(self, defaults: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge loaded settings with defaults"""
        result = defaults.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_settings(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_setting(self, path: str, default: Any = None) -> Any:
        """Get a setting value using dot notation (e.g., 'view.show_preview')"""
        keys = path.split('.')
        value = self.current_settings
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_setting(self, path: str, value: Any) -> bool:
        """Set a setting value using dot notation"""
        keys = path.split('.')
        setting_dict = self.current_settings
        
        try:
            # Navigate to the parent dictionary
            for key in keys[:-1]:
                if key not in setting_dict:
                    setting_dict[key] = {}
                setting_dict = setting_dict[key]
            
            # Set the final value
            setting_dict[keys[-1]] = value
            return True
            
        except (KeyError, TypeError):
            return False
    
    def get_theme_settings(self) -> Dict[str, Any]:
        """Get theme-related settings"""
        return self.current_settings.get('theme', {})
    
    def set_theme_settings(self, theme_id: str, custom_themes: Optional[Dict] = None):
        """Set theme settings"""
        self.set_setting('theme.current_theme_id', theme_id)
        if custom_themes is not None:
            self.set_setting('theme.custom_themes', custom_themes)
    
    def get_view_settings(self) -> Dict[str, Any]:
        """Get view-related settings"""
        return self.current_settings.get('view', {})
    
    def set_view_settings(self, **kwargs):
        """Set view settings"""
        for key, value in kwargs.items():
            self.set_setting(f'view.{key}', value)
    
    def get_window_settings(self) -> Dict[str, Any]:
        """Get window-related settings"""
        return self.current_settings.get('window', {})
    
    def set_window_settings(self, **kwargs):
        """Set window settings"""
        for key, value in kwargs.items():
            self.set_setting(f'window.{key}', value)
    
    def get_general_settings(self) -> Dict[str, Any]:
        """Get general settings"""
        return self.current_settings.get('general', {})
    
    def set_general_settings(self, **kwargs):
        """Set general settings"""
        for key, value in kwargs.items():
            self.set_setting(f'general.{key}', value)
    
    def get_advanced_settings(self) -> Dict[str, Any]:
        """Get advanced settings"""
        return self.current_settings.get('advanced', {})
    
    def set_advanced_settings(self, **kwargs):
        """Set advanced settings"""
        for key, value in kwargs.items():
            self.set_setting(f'advanced.{key}', value)
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        self.current_settings = self.default_settings.copy()
    
    def reset_section(self, section: str):
        """Reset a specific section to defaults"""
        if section in self.default_settings:
            self.current_settings[section] = self.default_settings[section].copy()
    
    def export_settings(self, file_path: str) -> bool:
        """Export settings to a file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, indent=2, ensure_ascii=False)
            return True
        except IOError:
            return False
    
    def import_settings(self, file_path: str) -> bool:
        """Import settings from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # Merge with defaults to ensure all keys exist
            self.current_settings = self.merge_settings(self.default_settings, imported_settings)
            return True
            
        except (json.JSONDecodeError, IOError):
            return False
    
    def get_startup_paths(self) -> Dict[str, str]:
        """Get startup paths for left and right panes"""
        general = self.get_general_settings()
        startup_paths = general.get('startup_paths', {})
        
        # Use current working directory as fallback
        cwd = os.getcwd()
        return {
            'left': startup_paths.get('left', cwd),
            'right': startup_paths.get('right', cwd)
        }
    
    def save_current_paths(self, left_path: str, right_path: str):
        """Save current paths for next startup"""
        if self.get_setting('general.remember_paths', True):
            self.set_setting('general.startup_paths.left', left_path)
            self.set_setting('general.startup_paths.right', right_path)
    
    def get_window_geometry(self) -> str:
        """Get saved window geometry"""
        return self.get_setting('window.geometry', '1200x800')
    
    def save_window_geometry(self, geometry: str):
        """Save window geometry"""
        if self.get_setting('general.restore_window', True):
            self.set_setting('window.geometry', geometry)
    
    def should_show_hidden_files(self) -> bool:
        """Check if hidden files should be shown"""
        return self.get_setting('view.show_hidden_files', False)

    # Layout Settings Methods
    def get_layout_mode(self) -> str:
        """Get current layout mode"""
        return self.get_setting('layout.mode', 'total_commander')

    def set_layout_mode(self, mode: str):
        """Set layout mode"""
        self.set_setting('layout.mode', mode)

    def get_layout_settings(self) -> dict:
        """Get all layout settings"""
        return self.get_setting('layout', {})

    def set_layout_settings(self, **kwargs):
        """Set multiple layout settings"""
        for key, value in kwargs.items():
            self.set_setting(f'layout.{key}', value)

    def get_pane_orientation(self) -> str:
        """Get pane orientation (horizontal/vertical)"""
        return self.get_setting('layout.pane_orientation', 'horizontal')

    def get_toolbar_position(self) -> str:
        """Get toolbar position"""
        return self.get_setting('layout.toolbar_position', 'top')

    def is_status_bar_visible(self) -> bool:
        """Check if status bar should be visible"""
        return self.get_setting('layout.status_bar_visible', True)

    def get_breadcrumb_style(self) -> str:
        """Get breadcrumb style"""
        return self.get_setting('layout.breadcrumb_style', 'buttons')

    def should_show_tree_view(self) -> bool:
        """Check if tree view should be shown (Windows Explorer mode)"""
        return self.get_setting('layout.show_tree_view', False)

    def get_pane_split_ratio(self) -> float:
        """Get pane split ratio"""
        return self.get_setting('layout.pane_split_ratio', 0.5)

    def get_preview_position(self) -> str:
        """Get preview pane position"""
        return self.get_setting('layout.preview_position', 'right')

    def should_show_toolbar_text(self) -> bool:
        """Check if toolbar should show text labels"""
        return self.get_setting('layout.show_toolbar_text', True)

    def get_toolbar_icon_size(self) -> str:
        """Get toolbar icon size"""
        return self.get_setting('layout.toolbar_icon_size', 'medium')
    
    def should_confirm_delete(self) -> bool:
        """Check if delete operations should be confirmed"""
        return self.get_setting('general.confirm_delete', True)
    
    def get_refresh_interval(self) -> int:
        """Get file list refresh interval in milliseconds"""
        return self.get_setting('advanced.refresh_interval', 1000)
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled"""
        return self.get_setting('advanced.debug_mode', False)
