#!/usr/bin/env python3
"""
Test script to verify UI fixes for breadcrumb edit and text visibility
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_breadcrumb_edit():
    """Test breadcrumb edit functionality"""
    print("Testing breadcrumb edit functionality...")
    
    try:
        from gexplorer.main import GExplorerApp
        
        # Create root window
        root = tk.Tk()
        root.title("GExplorer UI Test")
        root.geometry("800x600")
        
        # Create app instance
        app = GExplorerApp(root)
        
        # Test that path entries exist and are properly configured
        assert hasattr(app, 'left_path_entry'), "Left path entry not found"
        assert hasattr(app, 'right_path_entry'), "Right path entry not found"
        assert hasattr(app, 'left_breadcrumb_frame'), "Left breadcrumb frame not found"
        assert hasattr(app, 'right_breadcrumb_frame'), "Right breadcrumb frame not found"
        
        print("✓ Path entry components exist")
        
        # Test initial state - path entries should be hidden
        left_visible = app.left_path_entry.winfo_viewable()
        right_visible = app.right_path_entry.winfo_viewable()
        
        print(f"✓ Initial state - Left path entry visible: {left_visible}, Right path entry visible: {right_visible}")
        
        # Test toggle functionality
        print("Testing toggle functionality...")
        
        # Toggle left pane to edit mode
        app.toggle_path_edit('left')
        left_visible_after = app.left_path_entry.winfo_viewable()
        print(f"✓ After toggle - Left path entry visible: {left_visible_after}")
        
        # Toggle back to breadcrumb mode
        app.toggle_path_edit('left')
        left_visible_final = app.left_path_entry.winfo_viewable()
        print(f"✓ After toggle back - Left path entry visible: {left_visible_final}")
        
        print("✓ Breadcrumb edit functionality test passed!")
        
        # Show window for manual testing
        print("\nShowing window for manual testing...")
        print("- Click the ✏️ button to test path editing")
        print("- Check that file text is visible with good contrast")
        print("- Close the window when done testing")
        
        root.deiconify()  # Show the window
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ Breadcrumb edit test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_contrast():
    """Test color contrast in file view"""
    print("\nTesting color contrast...")
    
    try:
        from gexplorer.ui.file_view import FileView
        
        # Create a test window
        root = tk.Tk()
        root.withdraw()  # Hide the test window
        
        # Create file view
        file_view = FileView(root)
        
        # Check that color tags are configured
        tags = file_view.tag_names()
        expected_tags = ['directory', 'parent', 'hidden', 'executable', 'image', 'audio', 'video', 'code', 'oddrow', 'evenrow']
        
        for tag in expected_tags:
            if tag in tags:
                config = file_view.tag_configure(tag)
                print(f"✓ Tag '{tag}' configured: {config}")
            else:
                print(f"✗ Tag '{tag}' missing")
                return False
        
        root.destroy()
        print("✓ Color contrast test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Color contrast test failed: {e}")
        return False

if __name__ == "__main__":
    print("GExplorer UI Fixes Test")
    print("=" * 50)
    
    success = True
    
    # Run tests
    success &= test_color_contrast()
    success &= test_breadcrumb_edit()  # This will show the GUI for manual testing
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 UI fixes test completed!")
    else:
        print("❌ Some UI tests failed.")
    
    sys.exit(0 if success else 1)
