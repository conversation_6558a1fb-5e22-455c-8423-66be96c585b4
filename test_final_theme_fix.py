#!/usr/bin/env python3
"""
Final comprehensive test for the TreeView contrast fix
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_all_themes():
    """Test all themes to ensure proper contrast"""
    print("🎨 FINAL THEME CONTRAST TEST")
    print("=" * 60)
    
    from gexplorer.themes import ThemeManager
    theme_manager = ThemeManager()
    
    print("📋 Testing all available themes:")
    themes = theme_manager.get_all_themes()
    
    for theme_id, theme_data in themes.items():
        colors = theme_data.get('colors', {})
        bg = colors.get('bg_primary', '#ffffff')
        fg = colors.get('fg_primary', '#212529')
        base_theme = theme_data.get('base_theme', 'flatly')
        
        print(f"\n🎯 {theme_data.get('name', theme_id)}:")
        print(f"   Base Theme: {base_theme}")
        print(f"   Background: {bg}")
        print(f"   Foreground: {fg}")
        
        # Check contrast
        if bg == '#2b2b2b' and fg == '#ffffff':
            print("   ✅ Dark theme with proper white-on-dark contrast")
        elif bg == '#ffffff' and fg in ['#212529', '#000000']:
            print("   ✅ Light theme with proper dark-on-white contrast")
        else:
            print(f"   ⚠️ Custom contrast: {fg} on {bg}")
    
    print(f"\n🚀 STARTING COMPREHENSIVE TEST")
    print("=" * 60)
    
    print("🔧 FIXES IMPLEMENTED:")
    print("✅ FileView.apply_theme_colors() method added")
    print("✅ Theme-aware tag configuration for all file types")
    print("✅ Dark theme optimized colors for visibility")
    print("✅ Light theme maintains original good contrast")
    print("✅ Alternating row colors respect theme")
    print("✅ TreeView colors applied after pane creation")
    print("✅ Colors reapplied on theme changes")
    
    print(f"\n🧪 TESTING PROCEDURE:")
    print("1. App starts with saved theme")
    print("2. File lists should have proper contrast immediately")
    print("3. Click '🎨 Themes' to test theme switching")
    print("4. Try each theme and verify:")
    print("   - File list backgrounds match theme")
    print("   - Text is clearly readable")
    print("   - No white text on white background")
    print("   - File type colors are visible")
    print("   - Selection highlighting works")
    print("5. Test theme persistence by restarting")
    
    print(f"\n🎯 EXPECTED RESULTS:")
    print("✅ Dark Theme: Dark background (#2b2b2b) with white text (#ffffff)")
    print("✅ Light Theme: White background (#ffffff) with dark text (#212529)")
    print("✅ Blue Theme: Blue-tinted background with appropriate text")
    print("✅ Green Theme: Green-tinted background with appropriate text")
    print("✅ All themes: File type colors clearly visible")
    print("✅ All themes: Alternating row colors for better readability")
    
    # Import and start the application
    from ttkbootstrap import Style
    from gexplorer.main import GExplorerApp, get_saved_theme
    
    # Start with the saved theme's base theme
    base_theme = get_saved_theme()
    print(f"\n🎯 Starting with base theme: {base_theme}")
    
    style = Style(theme=base_theme)
    root = style.master
    app = GExplorerApp(root, style)
    
    print("\n🎨 App started! Check file list contrast and test theme switching.")
    print("❌ If you still see contrast issues, please report the specific theme and problem.")
    print("✅ Close the window when testing is complete.")
    
    app.run()

if __name__ == "__main__":
    test_all_themes()
