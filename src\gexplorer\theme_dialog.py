"""
Theme selection and editing dialog for GExplorer
"""

import tkinter as tk
from tkinter import ttk, colorchooser, messagebox, simpledialog
from typing import Callable, Dict, Any
from .themes import ThemeManager

class ThemeDialog:
    """Dialog for theme selection and editing"""
    
    def __init__(self, parent, current_theme_id: str, apply_callback: Callable[[str], None]):
        self.parent = parent
        self.current_theme_id = current_theme_id
        self.apply_callback = apply_callback
        self.theme_manager = ThemeManager()
        
        self.dialog = None
        self.selected_theme_id = current_theme_id
        self.theme_data = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create the theme dialog"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Theme Settings")
        self.dialog.geometry("600x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        self.create_widgets()
        self.load_themes()
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Theme selection frame
        selection_frame = ttk.LabelFrame(main_frame, text="Theme Selection", padding=10)
        selection_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Theme list
        ttk.Label(selection_frame, text="Available Themes:").pack(anchor=tk.W)
        
        list_frame = ttk.Frame(selection_frame)
        list_frame.pack(fill=tk.X, pady=(5, 10))
        
        self.theme_listbox = tk.Listbox(list_frame, height=6)
        theme_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.theme_listbox.yview)
        self.theme_listbox.configure(yscrollcommand=theme_scroll.set)
        
        self.theme_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        theme_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.theme_listbox.bind('<<ListboxSelect>>', self.on_theme_select)
        
        # Theme management buttons
        button_frame = ttk.Frame(selection_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Apply Theme", 
                  command=self.apply_theme).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Create Custom", 
                  command=self.create_custom_theme).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete Custom", 
                  command=self.delete_custom_theme).pack(side=tk.LEFT, padx=5)
        
        # Theme editing frame
        self.editing_frame = ttk.LabelFrame(main_frame, text="Theme Editing", padding=10)
        self.editing_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create notebook for different editing sections
        self.notebook = ttk.Notebook(self.editing_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Colors tab
        self.colors_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.colors_frame, text="Colors")
        self.create_colors_tab()
        
        # Fonts tab
        self.fonts_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.fonts_frame, text="Fonts")
        self.create_fonts_tab()
        
        # Dialog buttons
        dialog_buttons = ttk.Frame(main_frame)
        dialog_buttons.pack(fill=tk.X)
        
        ttk.Button(dialog_buttons, text="OK", 
                  command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(dialog_buttons, text="Cancel", 
                  command=self.cancel_clicked).pack(side=tk.RIGHT)
        ttk.Button(dialog_buttons, text="Apply", 
                  command=self.apply_clicked).pack(side=tk.RIGHT, padx=(0, 5))
    
    def create_colors_tab(self):
        """Create the colors editing tab"""
        # Scrollable frame for colors
        canvas = tk.Canvas(self.colors_frame)
        scrollbar = ttk.Scrollbar(self.colors_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.color_vars = {}
        self.color_buttons = {}
        
        # Color definitions with descriptions
        color_definitions = [
            ('bg_primary', 'Primary Background'),
            ('bg_secondary', 'Secondary Background'),
            ('bg_toolbar', 'Toolbar Background'),
            ('bg_pane', 'Pane Background'),
            ('bg_status', 'Status Bar Background'),
            ('fg_primary', 'Primary Text'),
            ('fg_secondary', 'Secondary Text'),
            ('border', 'Border Color'),
            ('accent', 'Accent Color'),
            ('hover', 'Hover Color'),
            ('pressed', 'Pressed Color'),
            ('selected', 'Selected Color'),
            ('selected_bg', 'Selected Background')
        ]
        
        for i, (color_key, description) in enumerate(color_definitions):
            row_frame = ttk.Frame(scrollable_frame)
            row_frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(row_frame, text=description, width=20).pack(side=tk.LEFT, padx=(0, 10))
            
            self.color_vars[color_key] = tk.StringVar()
            color_entry = ttk.Entry(row_frame, textvariable=self.color_vars[color_key], width=10)
            color_entry.pack(side=tk.LEFT, padx=(0, 5))
            
            color_button = tk.Button(row_frame, text="  ", width=3,
                                   command=lambda key=color_key: self.choose_color(key))
            color_button.pack(side=tk.LEFT)
            self.color_buttons[color_key] = color_button
    
    def create_fonts_tab(self):
        """Create the fonts editing tab"""
        self.font_vars = {}
        
        font_definitions = [
            ('default', 'Default Font'),
            ('toolbar', 'Toolbar Font'),
            ('nav', 'Navigation Font'),
            ('breadcrumb', 'Breadcrumb Font'),
            ('status', 'Status Font'),
            ('preview', 'Preview Font')
        ]
        
        for i, (font_key, description) in enumerate(font_definitions):
            row_frame = ttk.Frame(self.fonts_frame)
            row_frame.pack(fill=tk.X, pady=5, padx=10)
            
            ttk.Label(row_frame, text=description, width=15).pack(side=tk.LEFT, padx=(0, 10))
            
            font_frame = ttk.Frame(row_frame)
            font_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            # Font family
            self.font_vars[f"{font_key}_family"] = tk.StringVar()
            ttk.Label(font_frame, text="Family:").pack(side=tk.LEFT, padx=(0, 5))
            ttk.Entry(font_frame, textvariable=self.font_vars[f"{font_key}_family"], 
                     width=15).pack(side=tk.LEFT, padx=(0, 10))
            
            # Font size
            self.font_vars[f"{font_key}_size"] = tk.StringVar()
            ttk.Label(font_frame, text="Size:").pack(side=tk.LEFT, padx=(0, 5))
            ttk.Entry(font_frame, textvariable=self.font_vars[f"{font_key}_size"], 
                     width=5).pack(side=tk.LEFT)
    
    def load_themes(self):
        """Load available themes into the listbox"""
        self.theme_listbox.delete(0, tk.END)
        self.themes = self.theme_manager.get_available_themes()
        
        for theme_id, theme_name in self.themes.items():
            display_text = f"{theme_name}"
            if theme_id == self.current_theme_id:
                display_text += " (Current)"
            self.theme_listbox.insert(tk.END, display_text)
            
            # Select current theme
            if theme_id == self.current_theme_id:
                self.theme_listbox.selection_set(self.theme_listbox.size() - 1)
        
        # Load current theme data
        if self.current_theme_id:
            self.load_theme_data(self.current_theme_id)
    
    def on_theme_select(self, event):
        """Handle theme selection"""
        selection = self.theme_listbox.curselection()
        if selection:
            index = selection[0]
            theme_ids = list(self.themes.keys())
            if index < len(theme_ids):
                self.selected_theme_id = theme_ids[index]
                self.load_theme_data(self.selected_theme_id)
    
    def load_theme_data(self, theme_id: str):
        """Load theme data into editing controls"""
        try:
            self.theme_data = self.theme_manager.get_theme(theme_id)
            
            # Load colors
            colors = self.theme_data.get('colors', {})
            for color_key, color_var in self.color_vars.items():
                color_value = colors.get(color_key, '#000000')
                color_var.set(color_value)
                if color_key in self.color_buttons:
                    self.color_buttons[color_key].configure(bg=color_value)
            
            # Load fonts
            fonts = self.theme_data.get('fonts', {})
            for font_key, font_data in fonts.items():
                if isinstance(font_data, (list, tuple)) and len(font_data) >= 2:
                    family_var = self.font_vars.get(f"{font_key}_family")
                    size_var = self.font_vars.get(f"{font_key}_size")
                    if family_var:
                        family_var.set(font_data[0])
                    if size_var:
                        size_var.set(str(font_data[1]))
                        
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load theme data: {e}")
    
    def choose_color(self, color_key: str):
        """Open color chooser for a specific color"""
        current_color = self.color_vars[color_key].get()
        color = colorchooser.askcolor(color=current_color, title=f"Choose {color_key}")
        if color[1]:  # If a color was selected
            self.color_vars[color_key].set(color[1])
            self.color_buttons[color_key].configure(bg=color[1])
    
    def create_custom_theme(self):
        """Create a new custom theme"""
        name = simpledialog.askstring("Custom Theme", "Enter theme name:")
        if not name:
            return
        
        theme_id = name.lower().replace(' ', '_')
        
        # Check if theme already exists
        if theme_id in self.themes:
            messagebox.showerror("Error", "Theme with this name already exists!")
            return
        
        try:
            # Create based on currently selected theme
            base_theme_id = self.selected_theme_id or 'light'
            custom_theme = self.theme_manager.create_custom_theme(theme_id, name, base_theme_id)
            
            # Reload themes and select the new one
            self.load_themes()
            
            # Find and select the new theme
            theme_ids = list(self.themes.keys())
            if theme_id in theme_ids:
                index = theme_ids.index(theme_id)
                self.theme_listbox.selection_clear(0, tk.END)
                self.theme_listbox.selection_set(index)
                self.selected_theme_id = theme_id
                self.load_theme_data(theme_id)
            
            messagebox.showinfo("Success", f"Custom theme '{name}' created successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create custom theme: {e}")
    
    def delete_custom_theme(self):
        """Delete the selected custom theme"""
        if not self.selected_theme_id:
            return
        
        # Check if it's a custom theme
        theme_data = self.theme_manager.get_theme(self.selected_theme_id)
        if not theme_data.get('custom', False):
            messagebox.showwarning("Warning", "Cannot delete built-in themes!")
            return
        
        # Confirm deletion
        theme_name = self.themes[self.selected_theme_id]
        if messagebox.askyesno("Confirm Delete", f"Delete custom theme '{theme_name}'?"):
            try:
                if self.theme_manager.delete_theme(self.selected_theme_id):
                    self.load_themes()
                    messagebox.showinfo("Success", f"Theme '{theme_name}' deleted successfully!")
                else:
                    messagebox.showerror("Error", "Failed to delete theme!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete theme: {e}")
    
    def save_current_theme(self):
        """Save changes to the current theme"""
        if not self.theme_data:
            return
        
        try:
            # Update colors
            colors = {}
            for color_key, color_var in self.color_vars.items():
                colors[color_key] = color_var.get()
            
            # Update fonts
            fonts = {}
            font_keys = set()
            for var_name in self.font_vars.keys():
                if '_family' in var_name:
                    font_keys.add(var_name.replace('_family', ''))
            
            for font_key in font_keys:
                family_var = self.font_vars.get(f"{font_key}_family")
                size_var = self.font_vars.get(f"{font_key}_size")
                if family_var and size_var:
                    try:
                        size = int(size_var.get())
                        fonts[font_key] = (family_var.get(), size)
                    except ValueError:
                        fonts[font_key] = (family_var.get(), 9)  # Default size
            
            # Update theme data
            self.theme_data['colors'] = colors
            self.theme_data['fonts'] = fonts
            
            # Save if it's a custom theme
            if self.theme_data.get('custom', False):
                self.theme_manager.save_theme(self.selected_theme_id, self.theme_data)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save theme: {e}")
    
    def apply_theme(self):
        """Apply the selected theme"""
        if self.selected_theme_id:
            # Check if base theme is changing
            try:
                current_theme = self.theme_manager.get_theme(self.apply_callback.__self__.current_theme_id)
                new_theme = self.theme_manager.get_theme(self.selected_theme_id)

                current_base = current_theme.get('base_theme', 'flatly')
                new_base = new_theme.get('base_theme', 'flatly')

                if current_base != new_base:
                    # Show restart message for base theme changes
                    result = messagebox.askyesno(
                        "Theme Change",
                        f"Changing to '{new_theme['name']}' requires restarting the application.\n\n"
                        "Would you like to restart now?",
                        icon='question'
                    )
                    if result:
                        # Save the theme choice and restart
                        self.apply_callback(self.selected_theme_id)
                        self.restart_application(new_base)
                        return
                    else:
                        return

            except Exception as e:
                print(f"Error checking theme compatibility: {e}")

            # Apply theme normally (same base theme)
            self.apply_callback(self.selected_theme_id)

    def restart_application(self, new_base_theme):
        """Restart the application with new base theme"""
        try:
            import sys
            import subprocess

            # Close current dialog
            self.dialog.destroy()

            # Close main application
            self.parent.quit()

            # Restart with new theme
            subprocess.Popen([sys.executable] + sys.argv)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to restart application: {e}")
    
    def apply_clicked(self):
        """Handle Apply button click"""
        self.save_current_theme()
        self.apply_theme()
    
    def ok_clicked(self):
        """Handle OK button click"""
        self.save_current_theme()
        self.apply_theme()
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Handle Cancel button click"""
        self.dialog.destroy()
