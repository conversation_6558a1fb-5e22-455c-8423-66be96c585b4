import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *
import os
from typing import List, Dict, Callable, Optional
from PIL import Image, ImageTk
import math

class ExplorerView(tk.Frame):
    """
    A Windows Explorer-style file view with large icons and names below them.
    """
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Create scrollable canvas
        self.canvas = tk.Canvas(self, bg='white', highlightthickness=0)
        self.scrollbar_v = ttk.Scrollbar(self, orient=VERTICAL, command=self.canvas.yview)
        self.scrollbar_h = ttk.Scrollbar(self, orient=HORIZONTAL, command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=self.scrollbar_v.set, xscrollcommand=self.scrollbar_h.set)
        
        # Pack scrollbars and canvas
        self.scrollbar_v.pack(side=RIGHT, fill=Y)
        self.scrollbar_h.pack(side=BOTTOM, fill=X)
        self.canvas.pack(side=LEFT, fill=BOTH, expand=True)
        
        # Create frame inside canvas for file items
        self.items_frame = tk.Frame(self.canvas, bg='white')
        self.canvas_window = self.canvas.create_window(0, 0, anchor=NW, window=self.items_frame)
        
        # Configuration
        self.item_width = 120
        self.item_height = 140
        self.icon_size = 48
        self.padding = 15
        
        # File data and selection
        self.file_data: List[Dict] = []
        self.selected_item = None
        self.item_widgets = []
        
        # Callbacks
        self.double_click_callback: Optional[Callable] = None
        self.selection_callback: Optional[Callable] = None
        self.context_callback: Optional[Callable] = None

        # Theme colors (defaults)
        self.current_bg_color = 'white'
        self.current_fg_color = 'black'
        self.current_selected_bg = '#e3f2fd'
        
        # Bind events
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.items_frame.bind('<Configure>', self.on_frame_configure)
        self.bind_all('<MouseWheel>', self.on_mousewheel)
        
        # Default file icons (using emoji for now)
        self.file_icons = {
            'folder': '📁',
            'image': '🖼️',
            'document': '📄',
            'video': '🎬',
            'audio': '🎵',
            'archive': '📦',
            'executable': '⚙️',
            'default': '📄'
        }
        
    def on_canvas_configure(self, event):
        """Handle canvas resize"""
        # Update scroll region
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
        # Update items frame width to match canvas
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)
        
        # Recalculate layout
        self.layout_items()
        
    def on_frame_configure(self, event):
        """Handle frame resize"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
    def layout_items(self):
        """Layout file items in a grid"""
        if not self.file_data:
            return
            
        # Clear existing widgets
        for widget in self.item_widgets:
            widget.destroy()
        self.item_widgets.clear()
        
        # Calculate grid dimensions
        canvas_width = self.canvas.winfo_width()
        if canvas_width <= 1:  # Canvas not yet configured
            self.after(100, self.layout_items)
            return
            
        items_per_row = max(1, (canvas_width - self.padding) // (self.item_width + self.padding))
        
        row = 0
        col = 0
        
        for i, file_info in enumerate(self.file_data):
            # Calculate position
            x = col * (self.item_width + self.padding) + self.padding
            y = row * (self.item_height + self.padding) + self.padding
            
            # Create item widget
            item_widget = self.create_file_item(file_info, x, y, i)
            self.item_widgets.append(item_widget)
            
            # Move to next position
            col += 1
            if col >= items_per_row:
                col = 0
                row += 1
                
        # Update frame size
        total_rows = math.ceil(len(self.file_data) / items_per_row)
        frame_height = total_rows * (self.item_height + self.padding) + self.padding
        self.items_frame.configure(height=frame_height)
        
    def create_file_item(self, file_info: Dict, x: int, y: int, index: int) -> tk.Frame:
        """Create a single file item widget"""
        # Use current theme colors or defaults
        bg_color = getattr(self, 'current_bg_color', 'white')
        fg_color = getattr(self, 'current_fg_color', 'black')

        item_frame = tk.Frame(self.items_frame, bg=bg_color, width=self.item_width, height=self.item_height)
        item_frame.place(x=x, y=y)
        item_frame.pack_propagate(False)

        # Get file icon
        icon_text = self.get_file_icon(file_info)

        # Create icon label
        icon_label = tk.Label(item_frame, text=icon_text, font=('Segoe UI Emoji', 36),
                             bg=bg_color, fg=fg_color)
        icon_label.pack(pady=(8, 4))

        # Create name label (with text wrapping)
        name = file_info['name']
        if len(name) > 15:
            # Split long names
            if len(name) > 25:
                name = name[:22] + '...'
            # Try to break at reasonable points
            if ' ' in name:
                parts = name.split(' ')
                if len(parts) == 2 and len(parts[0]) < 12 and len(parts[1]) < 12:
                    name = f"{parts[0]}\n{parts[1]}"
            elif '.' in name and len(name) > 15:
                base, ext = os.path.splitext(name)
                if len(base) > 10:
                    name = f"{base[:10]}...\n{ext}"

        name_label = tk.Label(item_frame, text=name, font=('Segoe UI', 9),
                             bg=bg_color, fg=fg_color, justify=CENTER, wraplength=110)
        name_label.pack(pady=(0, 8))
        
        # Bind events
        def on_click(event, idx=index):
            self.select_item(idx)
            
        def on_double_click(event, idx=index):
            if self.double_click_callback:
                self.double_click_callback()
                
        def on_right_click(event, idx=index):
            self.select_item(idx)
            if self.context_callback:
                self.context_callback()
        
        # Bind to both frame and labels
        for widget in [item_frame, icon_label, name_label]:
            widget.bind('<Button-1>', on_click)
            widget.bind('<Double-Button-1>', on_double_click)
            widget.bind('<Button-3>', on_right_click)
            
        return item_frame
        
    def get_file_icon(self, file_info: Dict) -> str:
        """Get appropriate icon for file type"""
        if file_info['type'] == 'directory':
            if file_info['name'] == '..':
                return '⬆️'
            return self.file_icons['folder']
        
        name = file_info['name'].lower()
        ext = os.path.splitext(name)[1].lower()
        
        # Map extensions to icon types
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']:
            return self.file_icons['image']
        elif ext in ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv']:
            return self.file_icons['video']
        elif ext in ['.mp3', '.wav', '.flac', '.aac', '.ogg']:
            return self.file_icons['audio']
        elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return self.file_icons['archive']
        elif ext in ['.exe', '.msi', '.app', '.deb', '.rpm']:
            return self.file_icons['executable']
        elif ext in ['.txt', '.doc', '.docx', '.pdf', '.rtf']:
            return self.file_icons['document']
        else:
            return self.file_icons['default']
            
    def select_item(self, index: int):
        """Select an item"""
        # Clear previous selection
        if self.selected_item is not None and self.selected_item < len(self.item_widgets):
            old_widget = self.item_widgets[self.selected_item]
            # Use current theme background color
            bg_color = getattr(self, 'current_bg_color', 'white')
            old_widget.configure(bg=bg_color, relief='flat', borderwidth=0, highlightbackground=bg_color)
            for child in old_widget.winfo_children():
                if isinstance(child, tk.Label):
                    child.configure(bg=bg_color)

        # Set new selection
        self.selected_item = index
        if index < len(self.item_widgets):
            widget = self.item_widgets[index]
            # Use current theme selection color
            selected_bg = getattr(self, 'current_selected_bg', '#e3f2fd')
            widget.configure(bg=selected_bg, relief='solid', borderwidth=1, highlightbackground='#2196f3')
            for child in widget.winfo_children():
                if isinstance(child, tk.Label):
                    child.configure(bg=selected_bg)

        # Call selection callback
        if self.selection_callback:
            self.selection_callback()
            
    def populate(self, files: List[Dict]):
        """Populate the view with files"""
        self.file_data = files
        self.selected_item = None
        self.layout_items()
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file name"""
        if self.selected_item is not None and self.selected_item < len(self.file_data):
            return self.file_data[self.selected_item]['name']
        return None
        
    def get_selected_file_info(self) -> Optional[Dict]:
        """Get the currently selected file info"""
        if self.selected_item is not None and self.selected_item < len(self.file_data):
            return self.file_data[self.selected_item]
        return None
        
    def set_double_click_callback(self, callback: Callable):
        """Set the double-click callback"""
        self.double_click_callback = callback
        
    def set_selection_callback(self, callback: Callable):
        """Set the selection callback"""
        self.selection_callback = callback
        
    def set_context_callback(self, callback: Callable):
        """Set the context menu callback"""
        self.context_callback = callback
        
    def apply_theme_colors(self, bg_color: str, fg_color: str, selected_bg: str):
        """Apply theme colors to the view"""
        # Store current theme colors
        self.current_bg_color = bg_color
        self.current_fg_color = fg_color
        self.current_selected_bg = selected_bg

        self.canvas.configure(bg=bg_color)
        self.items_frame.configure(bg=bg_color)

        # Update existing items, preserving selection
        for i, widget in enumerate(self.item_widgets):
            if i == self.selected_item:
                # Keep selection highlighting
                widget.configure(bg=selected_bg, relief='solid', borderwidth=1, highlightbackground='#2196f3')
                for child in widget.winfo_children():
                    if isinstance(child, tk.Label):
                        child.configure(bg=selected_bg, fg=fg_color)
            else:
                # Apply normal background
                widget.configure(bg=bg_color, relief='flat', borderwidth=0, highlightbackground=bg_color)
                for child in widget.winfo_children():
                    if isinstance(child, tk.Label):
                        child.configure(bg=bg_color, fg=fg_color)
