#!/usr/bin/env python3
"""
Create a test image for testing the image preview functionality
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    # Create a test image
    width, height = 800, 600
    image = Image.new('RGB', (width, height), color='lightblue')
    draw = ImageDraw.Draw(image)
    
    # Draw some shapes and text
    draw.rectangle([50, 50, width-50, height-50], outline='darkblue', width=5)
    draw.ellipse([150, 150, width-150, height-150], fill='yellow', outline='orange', width=3)
    
    # Add text
    try:
        # Try to use a system font
        font = ImageFont.truetype("arial.ttf", 36)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    text = "Test Image for GExplorer"
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    text_x = (width - text_width) // 2
    text_y = (height - text_height) // 2
    
    draw.text((text_x, text_y), text, fill='darkblue', font=font)
    
    # Add smaller text
    small_text = "Click to enlarge in preview!"
    small_bbox = draw.textbbox((0, 0), small_text, font=font)
    small_width = small_bbox[2] - small_bbox[0]
    small_x = (width - small_width) // 2
    small_y = text_y + text_height + 20
    
    draw.text((small_x, small_y), small_text, fill='red', font=font)
    
    # Save the image
    image.save('test_image.png')
    print("✓ Test image created: test_image.png")
    print(f"  Size: {width}x{height} pixels")
    print("  Use this image to test the preview and enlarge functionality!")
    
except ImportError:
    print("❌ Pillow (PIL) not available. Cannot create test image.")
except Exception as e:
    print(f"❌ Error creating test image: {e}")
