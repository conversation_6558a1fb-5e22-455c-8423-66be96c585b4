import os
from pathlib import Path
import shutil
import subprocess
from typing import List, Dict, Optional

def list_directory(path: str = '.') -> List[Dict[str, str]]:
    """
    List the contents of a directory.
    
    Args:
        path (str): The path to the directory to list. Defaults to current directory.
        
    Returns:
        List[Dict[str, str]]: A list of dictionaries containing information about each item.
    """
    try:
        directory = Path(path)
        if not directory.exists():
            raise FileNotFoundError(f"Directory not found: {path}")
        if not directory.is_dir():
            raise NotADirectoryError(f"Path is not a directory: {path}")
            
        items = []
        for item in directory.iterdir():
            item_info = {
                'name': item.name,
                'path': str(item),
                'type': 'directory' if item.is_dir() else 'file',
                'size': str(item.stat().st_size) if item.is_file() else 'N/A',
                'last_modified': str(item.stat().st_mtime)
            }
            items.append(item_info)
        return items
    except Exception as e:
        print(f"Error listing directory {path}: {str(e)}")
        return []

def open_file(path: str) -> bool:
    """
    Open a file using the default system application.
    
    Args:
        path (str): The path to the file to open.
        
    Returns:
        bool: True if the file was opened successfully, False otherwise.
    """
    try:
        file_path = Path(path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {path}")
        if not file_path.is_file():
            raise IsADirectoryError(f"Path is not a file: {path}")
            
        os.startfile(str(file_path))  # Windows-specific; use 'open' on macOS or 'xdg-open' on Linux
        return True
    except Exception as e:
        print(f"Error opening file {path}: {str(e)}")
        return False

def copy_item(source: str, destination: str) -> bool:
    """
    Copy a file or directory to a new location.
    
    Args:
        source (str): The path to the source item.
        destination (str): The path to the destination.
        
    Returns:
        bool: True if the copy operation was successful, False otherwise.
    """
    try:
        src_path = Path(source)
        dst_path = Path(destination)
        if not src_path.exists():
            raise FileNotFoundError(f"Source not found: {source}")
            
        if src_path.is_dir():
            shutil.copytree(str(src_path), str(dst_path), dirs_exist_ok=True)
        else:
            shutil.copy2(str(src_path), str(dst_path))
        return True
    except Exception as e:
        print(f"Error copying from {source} to {destination}: {str(e)}")
        return False

def move_item(source: str, destination: str) -> bool:
    """
    Move a file or directory to a new location.
    
    Args:
        source (str): The path to the source item.
        destination (str): The path to the destination.
        
    Returns:
        bool: True if the move operation was successful, False otherwise.
    """
    try:
        src_path = Path(source)
        dst_path = Path(destination)
        if not src_path.exists():
            raise FileNotFoundError(f"Source not found: {source}")
            
        shutil.move(str(src_path), str(dst_path))
        return True
    except Exception as e:
        print(f"Error moving from {source} to {destination}: {str(e)}")
        return False

def delete_item(path: str) -> bool:
    """
    Delete a file or directory.
    
    Args:
        path (str): The path to the item to delete.
        
    Returns:
        bool: True if the delete operation was successful, False otherwise.
    """
    try:
        item_path = Path(path)
        if not item_path.exists():
            raise FileNotFoundError(f"Item not found: {path}")
            
        if item_path.is_dir():
            shutil.rmtree(str(item_path))
        else:
            item_path.unlink()
        return True
    except Exception as e:
        print(f"Error deleting {path}: {str(e)}")
        return False

def rename_item(old_path: str, new_name: str) -> bool:
    """
    Rename a file or directory.
    
    Args:
        old_path (str): The current path to the item.
        new_name (str): The new name for the item.
        
    Returns:
        bool: True if the rename operation was successful, False otherwise.
    """
    try:
        old_item_path = Path(old_path)
        if not old_item_path.exists():
            raise FileNotFoundError(f"Item not found: {old_path}")
            
        new_item_path = old_item_path.parent / new_name
        old_item_path.rename(new_item_path)
        return True
    except Exception as e:
        print(f"Error renaming {old_path} to {new_name}: {str(e)}")
        return False

def create_directory(path: str) -> bool:
    """
    Create a new directory.
    
    Args:
        path (str): The path for the new directory.
        
    Returns:
        bool: True if the directory was created successfully, False otherwise.
    """
    try:
        new_dir_path = Path(path)
        new_dir_path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        print(f"Error creating directory {path}: {str(e)}")
        return False

def get_file_info(path: str) -> Optional[Dict[str, str]]:
    """
    Get detailed information about a file or directory.
    
    Args:
        path (str): The path to the item.
        
    Returns:
        Optional[Dict[str, str]]: A dictionary with detailed information about the item, or None if an error occurs.
    """
    try:
        item_path = Path(path)
        if not item_path.exists():
            raise FileNotFoundError(f"Item not found: {path}")
            
        info = {
            'name': item_path.name,
            'path': str(item_path),
            'type': 'directory' if item_path.is_dir() else 'file',
            'size': str(item_path.stat().st_size) if item_path.is_file() else 'N/A',
            'created': str(item_path.stat().st_ctime),
            'last_modified': str(item_path.stat().st_mtime),
            'last_accessed': str(item_path.stat().st_atime)
        }
        return info
    except Exception as e:
        print(f"Error getting info for {path}: {str(e)}")
        return None
