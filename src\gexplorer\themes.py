"""
Theme management system for GExplorer
"""

import json
import os
from typing import Dict, Any

class ThemeManager:
    """Manages themes for the GExplorer application"""
    
    def __init__(self):
        self.themes_dir = os.path.join(os.path.dirname(__file__), 'themes')
        self.ensure_themes_dir()
        self.current_theme = None
        self.load_default_themes()
    
    def ensure_themes_dir(self):
        """Ensure themes directory exists"""
        if not os.path.exists(self.themes_dir):
            os.makedirs(self.themes_dir)
    
    def load_default_themes(self):
        """Load default theme definitions"""
        self.default_themes = {
            'light': {
                'name': 'Light Theme',
                'base_theme': 'flatly',
                'colors': {
                    'bg_primary': '#ffffff',
                    'bg_secondary': '#f8f9fa',
                    'bg_toolbar': '#e9ecef',
                    'bg_pane': '#ffffff',
                    'bg_status': '#f8f9fa',
                    'fg_primary': '#212529',
                    'fg_secondary': '#6c757d',
                    'border': '#dee2e6',
                    'accent': '#007bff',
                    'hover': '#e9ecef',
                    'pressed': '#dee2e6',
                    'selected': '#007bff',
                    'selected_bg': '#e3f2fd'
                },
                'fonts': {
                    'default': ('Segoe UI', 9),
                    'toolbar': ('Segoe UI', 9),
                    'nav': ('Segoe UI', 10),
                    'breadcrumb': ('Segoe UI', 9),
                    'status': ('Segoe UI', 8),
                    'preview': ('Consolas', 9)
                }
            },
            'dark': {
                'name': 'Dark Theme',
                'base_theme': 'darkly',
                'colors': {
                    'bg_primary': '#212529',
                    'bg_secondary': '#343a40',
                    'bg_toolbar': '#495057',
                    'bg_pane': '#2b3035',
                    'bg_status': '#343a40',
                    'fg_primary': '#ffffff',
                    'fg_secondary': '#adb5bd',
                    'border': '#495057',
                    'accent': '#0d6efd',
                    'hover': '#495057',
                    'pressed': '#6c757d',
                    'selected': '#0d6efd',
                    'selected_bg': '#1e3a5f'
                },
                'fonts': {
                    'default': ('Segoe UI', 9),
                    'toolbar': ('Segoe UI', 9),
                    'nav': ('Segoe UI', 10),
                    'breadcrumb': ('Segoe UI', 9),
                    'status': ('Segoe UI', 8),
                    'preview': ('Consolas', 9)
                }
            },
            'blue': {
                'name': 'Blue Theme',
                'base_theme': 'litera',
                'colors': {
                    'bg_primary': '#f8f9fa',
                    'bg_secondary': '#e9ecef',
                    'bg_toolbar': '#d1ecf1',
                    'bg_pane': '#ffffff',
                    'bg_status': '#e9ecef',
                    'fg_primary': '#212529',
                    'fg_secondary': '#495057',
                    'border': '#bee5eb',
                    'accent': '#17a2b8',
                    'hover': '#bee5eb',
                    'pressed': '#a6d9e6',
                    'selected': '#17a2b8',
                    'selected_bg': '#d1ecf1'
                },
                'fonts': {
                    'default': ('Segoe UI', 9),
                    'toolbar': ('Segoe UI', 9),
                    'nav': ('Segoe UI', 10),
                    'breadcrumb': ('Segoe UI', 9),
                    'status': ('Segoe UI', 8),
                    'preview': ('Consolas', 9)
                }
            },
            'green': {
                'name': 'Green Theme',
                'base_theme': 'minty',
                'colors': {
                    'bg_primary': '#f8f9fa',
                    'bg_secondary': '#e9ecef',
                    'bg_toolbar': '#d4edda',
                    'bg_pane': '#ffffff',
                    'bg_status': '#e9ecef',
                    'fg_primary': '#212529',
                    'fg_secondary': '#495057',
                    'border': '#c3e6cb',
                    'accent': '#28a745',
                    'hover': '#c3e6cb',
                    'pressed': '#b1dfbb',
                    'selected': '#28a745',
                    'selected_bg': '#d4edda'
                },
                'fonts': {
                    'default': ('Segoe UI', 9),
                    'toolbar': ('Segoe UI', 9),
                    'nav': ('Segoe UI', 10),
                    'breadcrumb': ('Segoe UI', 9),
                    'status': ('Segoe UI', 8),
                    'preview': ('Consolas', 9)
                }
            }
        }
    
    def get_available_themes(self) -> Dict[str, str]:
        """Get list of available themes"""
        themes = {}
        
        # Add default themes
        for theme_id, theme_data in self.default_themes.items():
            themes[theme_id] = theme_data['name']
        
        # Add custom themes from files
        if os.path.exists(self.themes_dir):
            for filename in os.listdir(self.themes_dir):
                if filename.endswith('.json'):
                    theme_id = filename[:-5]  # Remove .json extension
                    try:
                        theme_data = self.load_theme_file(theme_id)
                        themes[theme_id] = theme_data.get('name', theme_id.title())
                    except:
                        pass  # Skip invalid theme files
        
        return themes
    
    def get_theme(self, theme_id: str) -> Dict[str, Any]:
        """Get theme data by ID"""
        if theme_id in self.default_themes:
            return self.default_themes[theme_id].copy()
        
        # Try to load from file
        return self.load_theme_file(theme_id)
    
    def load_theme_file(self, theme_id: str) -> Dict[str, Any]:
        """Load theme from JSON file"""
        theme_path = os.path.join(self.themes_dir, f"{theme_id}.json")
        with open(theme_path, 'r') as f:
            return json.load(f)
    
    def save_theme(self, theme_id: str, theme_data: Dict[str, Any]):
        """Save theme to JSON file"""
        theme_path = os.path.join(self.themes_dir, f"{theme_id}.json")
        with open(theme_path, 'w') as f:
            json.dump(theme_data, f, indent=2)
    
    def delete_theme(self, theme_id: str) -> bool:
        """Delete a custom theme (cannot delete default themes)"""
        if theme_id in self.default_themes:
            return False  # Cannot delete default themes
        
        theme_path = os.path.join(self.themes_dir, f"{theme_id}.json")
        if os.path.exists(theme_path):
            os.remove(theme_path)
            return True
        return False
    
    def create_custom_theme(self, theme_id: str, name: str, base_theme_id: str = 'light') -> Dict[str, Any]:
        """Create a new custom theme based on an existing theme"""
        base_theme = self.get_theme(base_theme_id)
        
        custom_theme = {
            'name': name,
            'base_theme': base_theme['base_theme'],
            'colors': base_theme['colors'].copy(),
            'fonts': base_theme['fonts'].copy(),
            'custom': True
        }
        
        self.save_theme(theme_id, custom_theme)
        return custom_theme
