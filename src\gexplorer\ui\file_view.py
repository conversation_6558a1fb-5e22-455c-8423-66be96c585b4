import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *
from typing import List, Dict
from datetime import datetime

class FileView(ttk.Treeview):
    """
    A widget to display a list of files in the current directory.
    """
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Define columns for the file list
        self['columns'] = ('name', 'size', 'last_modified')
        self.column('#0', width=0, stretch=False)  # Hide the default first column
        self.column('name', anchor=W, width=200, minwidth=100)
        self.column('size', anchor=E, width=100, minwidth=50)
        self.column('last_modified', anchor=W, width=150, minwidth=100)
        
        # Define headings
        self.heading('name', text='Name', anchor=W)
        self.heading('size', text='Size', anchor=E)
        self.heading('last_modified', text='Last Modified', anchor=W)
        
        # Add a scrollbar
        scrollbar = ttk.Scrollbar(parent, orient=VERTICAL, command=self.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.configure(yscrollcommand=scrollbar.set)
        
        # Horizontal scrollbar
        h_scrollbar = ttk.Scrollbar(parent, orient=HORIZONTAL, command=self.xview)
        h_scrollbar.pack(side=BOTTOM, fill=X)
        self.configure(xscrollcommand=h_scrollbar.set)
        
    def populate(self, files: List[Dict[str, str]]):
        """
        Populate the file list with data.
        
        Args:
            files (List[Dict[str, str]]): List of dictionaries containing file information.
        """
        # Clear existing items
        for item in self.get_children():
            self.delete(item)
            
        # Add new items
        for file_info in files:
            last_modified = datetime.fromtimestamp(float(file_info['last_modified'])).strftime('%Y-%m-%d %H:%M:%S')
            self.insert('', END, values=(
                file_info['name'],
                file_info['size'],
                last_modified
            ))
            
    def get_selected_file(self) -> str:
        """
        Get the path of the currently selected file.
        
        Returns:
            str: Path of the selected file, or empty string if no selection.
        """
        selection = self.selection()
        if selection:
            item = self.item(selection[0])
            return item['values'][0] if item['values'] else ''
        return ''
        
    def show_context_menu(self, event, callback):
        """
        Show a context menu for file operations at the clicked position.
        
        Args:
            event: The event object containing the coordinates of the click.
            callback: A function to call with the selected operation and file name.
        """
        selection = self.selection()
        if selection:
            menu = tk.Menu(self, tearoff=0)
            menu.add_command(label="Open", command=lambda: callback("open", self.get_selected_file()))
            menu.add_command(label="Copy", command=lambda: callback("copy", self.get_selected_file()))
            menu.add_command(label="Move", command=lambda: callback("move", self.get_selected_file()))
            menu.add_command(label="Delete", command=lambda: callback("delete", self.get_selected_file()))
            menu.add_command(label="Rename", command=lambda: callback("rename", self.get_selected_file()))
            menu.post(event.x_root, event.y_root)
