import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *
from typing import List, Dict, Callable, Optional
from datetime import datetime
import os

class FileView(ttk.Treeview):
    """
    A modern file view widget with context menus, keyboard shortcuts, and drag & drop support.
    """
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # Define columns for the file list
        self['columns'] = ('name', 'type', 'size', 'last_modified')
        self.column('#0', width=0, stretch=False)  # Hide the default first column
        self.column('name', anchor=W, width=250, minwidth=150)
        self.column('type', anchor=W, width=80, minwidth=60)
        self.column('size', anchor=E, width=100, minwidth=70)
        self.column('last_modified', anchor=W, width=150, minwidth=120)

        # Define headings with sorting capability
        self.heading('name', text='Name', anchor=W, command=lambda: self.sort_by_column('name'))
        self.heading('type', text='Type', anchor=W, command=lambda: self.sort_by_column('type'))
        self.heading('size', text='Size', anchor=E, command=lambda: self.sort_by_column('size'))
        self.heading('last_modified', text='Modified', anchor=W, command=lambda: self.sort_by_column('last_modified'))

        # Add scrollbars
        scrollbar = ttk.Scrollbar(parent, orient=VERTICAL, command=self.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.configure(yscrollcommand=scrollbar.set)

        h_scrollbar = ttk.Scrollbar(parent, orient=HORIZONTAL, command=self.xview)
        h_scrollbar.pack(side=BOTTOM, fill=X)
        self.configure(xscrollcommand=h_scrollbar.set)

        # Initialize callbacks
        self.context_callback: Optional[Callable] = None
        self.keyboard_callback: Optional[Callable] = None

        # Sorting state
        self.sort_column = 'name'
        self.sort_reverse = False

        # Store file data for sorting
        self.file_data: List[Dict] = []

        # Bind events
        self.bind('<Button-3>', self.show_context_menu)  # Right-click
        self.bind('<Key>', self.handle_keyboard)
        self.focus_set()  # Allow keyboard focus

    def populate(self, files: List[Dict[str, str]]):
        """
        Populate the file list with data.

        Args:
            files (List[Dict[str, str]]): List of dictionaries containing file information.
        """
        # Store file data for sorting
        self.file_data = files.copy()

        # Sort the data
        self.sort_data()

        # Clear existing items
        for item in self.get_children():
            self.delete(item)

        # Add new items
        for file_info in self.file_data:
            # Format file type
            file_type = self.get_file_type(file_info)

            # Format size
            size_str = self.format_size(file_info['size'])

            # Format date
            try:
                if file_info['last_modified'] != '0':
                    last_modified = datetime.fromtimestamp(float(file_info['last_modified'])).strftime('%Y-%m-%d %H:%M')
                else:
                    last_modified = ''
            except:
                last_modified = ''

            # Insert with appropriate tags for styling
            tags = []
            if file_info['type'] == 'directory':
                if file_info['name'] == '..':
                    tags.append('parent')
                else:
                    tags.append('directory')
            elif file_info['name'].startswith('.'):
                tags.append('hidden')
            else:
                # Add tag based on file extension for different styling
                if '.' in file_info['name']:
                    ext = file_info['name'].split('.')[-1].lower()
                    if ext in ['exe', 'msi', 'app']:
                        tags.append('executable')
                    elif ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg']:
                        tags.append('image')
                    elif ext in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
                        tags.append('audio')
                    elif ext in ['mp4', 'avi', 'mkv', 'mov', 'wmv']:
                        tags.append('video')
                    elif ext in ['py', 'js', 'html', 'css', 'cpp', 'c', 'java']:
                        tags.append('code')

            # Add alternating row color
            row_index = len(self.get_children())
            if row_index % 2 == 0:
                tags.append('evenrow')
            else:
                tags.append('oddrow')

            self.insert('', END, values=(
                file_info['name'],
                file_type,
                size_str,
                last_modified
            ), tags=tags)

        # Configure tags for visual styling
        self.tag_configure('directory', foreground='#2E86AB', font=('Segoe UI', 9, 'bold'))
        self.tag_configure('parent', foreground='#666666', font=('Segoe UI', 9, 'bold'))
        self.tag_configure('hidden', foreground='#999999', font=('Segoe UI', 9, 'italic'))
        self.tag_configure('executable', foreground='#D32F2F', font=('Segoe UI', 9))
        self.tag_configure('image', foreground='#7B1FA2', font=('Segoe UI', 9))
        self.tag_configure('audio', foreground='#F57C00', font=('Segoe UI', 9))
        self.tag_configure('video', foreground='#C2185B', font=('Segoe UI', 9))
        self.tag_configure('code', foreground='#388E3C', font=('Segoe UI', 9))

        # Configure alternating row colors
        self.tag_configure('oddrow', background='#f8f8f8')
        self.tag_configure('evenrow', background='#ffffff')

    def get_file_type(self, file_info: Dict) -> str:
        """Get a user-friendly file type description with icons"""
        if file_info['type'] == 'directory':
            if file_info['name'] == '..':
                return '📁 Parent'
            return '📁 Folder'
        else:
            name = file_info['name']
            if '.' in name:
                ext = name.split('.')[-1].lower()
                icon = self.get_file_icon(ext)
                return f'{icon} {ext.upper()}'
            return '📄 File'

    def get_file_icon(self, extension: str) -> str:
        """Get an appropriate icon for the file extension"""
        icon_map = {
            # Documents
            'txt': '📄', 'doc': '📄', 'docx': '📄', 'pdf': '📕',
            'rtf': '📄', 'odt': '📄',

            # Spreadsheets
            'xls': '📊', 'xlsx': '📊', 'csv': '📊', 'ods': '📊',

            # Presentations
            'ppt': '📊', 'pptx': '📊', 'odp': '📊',

            # Images
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
            'bmp': '🖼️', 'svg': '🖼️', 'ico': '🖼️', 'tiff': '🖼️',

            # Videos
            'mp4': '🎬', 'avi': '🎬', 'mkv': '🎬', 'mov': '🎬',
            'wmv': '🎬', 'flv': '🎬', 'webm': '🎬',

            # Audio
            'mp3': '🎵', 'wav': '🎵', 'flac': '🎵', 'aac': '🎵',
            'ogg': '🎵', 'wma': '🎵',

            # Archives
            'zip': '📦', 'rar': '📦', '7z': '📦', 'tar': '📦',
            'gz': '📦', 'bz2': '📦',

            # Code files
            'py': '🐍', 'js': '📜', 'html': '🌐', 'css': '🎨',
            'cpp': '⚙️', 'c': '⚙️', 'java': '☕', 'php': '🐘',
            'rb': '💎', 'go': '🐹', 'rs': '🦀', 'swift': '🦉',

            # Executables
            'exe': '⚙️', 'msi': '⚙️', 'deb': '📦', 'rpm': '📦',
            'dmg': '💿', 'app': '📱',
        }

        return icon_map.get(extension, '📄')

    def format_size(self, size_str: str) -> str:
        """Format file size in human-readable format"""
        if size_str == 'N/A' or size_str == '':
            return ''

        try:
            size = int(size_str)
            if size < 1024:
                return f'{size} B'
            elif size < 1024 * 1024:
                return f'{size // 1024} KB'
            elif size < 1024 * 1024 * 1024:
                return f'{size // (1024 * 1024)} MB'
            else:
                return f'{size // (1024 * 1024 * 1024)} GB'
        except:
            return size_str

    def sort_by_column(self, column: str):
        """Sort the file list by the specified column"""
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False

        self.sort_data()
        self.populate(self.file_data)

    def sort_data(self):
        """Sort the file data based on current sort settings"""
        if not self.file_data:
            return

        def sort_key(item):
            # Always put parent directory (..) first
            if item['name'] == '..':
                return (0, '')

            # Then directories
            if item['type'] == 'directory':
                return (1, item['name'].lower())

            # Then files
            if self.sort_column == 'name':
                return (2, item['name'].lower())
            elif self.sort_column == 'type':
                return (2, self.get_file_type(item).lower())
            elif self.sort_column == 'size':
                try:
                    return (2, int(item['size']) if item['size'] != 'N/A' else 0)
                except:
                    return (2, 0)
            elif self.sort_column == 'last_modified':
                try:
                    return (2, float(item['last_modified']))
                except:
                    return (2, 0)
            return (2, item['name'].lower())

        self.file_data.sort(key=sort_key, reverse=self.sort_reverse)

    def get_selected_file(self) -> str:
        """
        Get the name of the currently selected file.

        Returns:
            str: Name of the selected file, or empty string if no selection.
        """
        selection = self.selection()
        if selection:
            item = self.item(selection[0])
            return item['values'][0] if item['values'] else ''
        return ''

    def get_selected_files(self) -> List[str]:
        """
        Get the names of all currently selected files.

        Returns:
            List[str]: List of selected file names.
        """
        selection = self.selection()
        files = []
        for item_id in selection:
            item = self.item(item_id)
            if item['values']:
                files.append(item['values'][0])
        return files

    def set_context_callback(self, callback: Callable):
        """Set the callback function for context menu actions"""
        self.context_callback = callback

    def set_keyboard_callback(self, callback: Callable):
        """Set the callback function for keyboard shortcuts"""
        self.keyboard_callback = callback

    def show_context_menu(self, event):
        """
        Show a context menu for file operations at the clicked position.

        Args:
            event: The event object containing the coordinates of the click.
        """
        # Select the item under cursor
        item = self.identify_row(event.y)
        if item:
            self.selection_set(item)

        selected_file = self.get_selected_file()
        if selected_file and self.context_callback:
            menu = tk.Menu(self, tearoff=0)

            if selected_file == '..':
                menu.add_command(label="Go Up", command=lambda: self.context_callback("go_up", selected_file))
            else:
                menu.add_command(label="Open", command=lambda: self.context_callback("open", selected_file))
                menu.add_separator()
                menu.add_command(label="Copy", command=lambda: self.context_callback("copy", selected_file))
                menu.add_command(label="Cut", command=lambda: self.context_callback("cut", selected_file))
                menu.add_separator()
                menu.add_command(label="Delete", command=lambda: self.context_callback("delete", selected_file))
                menu.add_command(label="Rename", command=lambda: self.context_callback("rename", selected_file))
                menu.add_separator()
                menu.add_command(label="Properties", command=lambda: self.context_callback("properties", selected_file))

            menu.post(event.x_root, event.y_root)

    def handle_keyboard(self, event):
        """
        Handle keyboard shortcuts.

        Args:
            event: The keyboard event.
        """
        if not self.keyboard_callback:
            return

        selected_file = self.get_selected_file()
        if not selected_file:
            return

        # Handle keyboard shortcuts
        if event.keysym == 'Return':  # Enter key
            self.keyboard_callback("open", selected_file)
        elif event.keysym == 'Delete':  # Delete key
            self.keyboard_callback("delete", selected_file)
        elif event.keysym == 'F2':  # F2 for rename
            self.keyboard_callback("rename", selected_file)
        elif event.state & 0x4 and event.keysym == 'c':  # Ctrl+C for copy
            self.keyboard_callback("copy", selected_file)
        elif event.state & 0x4 and event.keysym == 'x':  # Ctrl+X for cut
            self.keyboard_callback("cut", selected_file)
        elif event.state & 0x4 and event.keysym == 'v':  # Ctrl+V for paste
            self.keyboard_callback("paste", selected_file)
        elif event.state & 0x4 and event.keysym == 'a':  # Ctrl+A for select all
            self.select_all()
        elif event.keysym == 'BackSpace':  # Backspace to go up
            self.keyboard_callback("go_up", selected_file)

    def select_all(self):
        """Select all items in the file view"""
        all_items = self.get_children()
        self.selection_set(all_items)

    def clear_selection(self):
        """Clear the current selection"""
        self.selection_remove(self.selection())

    def refresh(self):
        """Refresh the file view with current data"""
        if self.file_data:
            self.populate(self.file_data)
